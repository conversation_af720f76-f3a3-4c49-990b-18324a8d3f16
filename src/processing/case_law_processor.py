"""
Case Law Processing System
Orchestrates the end-to-end workflow for fetching, processing, and storing case law data
from Court Listener and other sources with support for multiple jurisdictions.
Includes role-based access control and multi-jurisdiction support.
"""

import os
import uuid
import json
import logging
import time
from typing import Dict, List, Optional, Union, Any, Set
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Configure logging
os.makedirs("logs", exist_ok=True)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class CaseLawProcessor:
    """
    Main orchestrator for case law processing.
    Handles the workflow for fetching, processing, and storing case law data
    with support for multiple storage backends and jurisdictions.
    Includes role-based access control for multi-tenant usage.
    """
    
    def __init__(self, config: Optional[Dict] = None, user_id: Optional[str] = None, user_role: Optional[str] = None, tenant_id: Optional[str] = None):
        """
        Initialize the case law processor.
        
        Args:
            config: Optional configuration dictionary to override defaults
            user_id: Optional user ID for access control and audit
            user_role: Optional user role for permission checks (partner, attorney, paralegal, staff, client)
            tenant_id: Optional tenant ID for multi-tenant isolation
        """
        self.config = {
            "gcs_bucket": os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"),
            "pinecone_index": os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large"),
            "batch_size": 10,
            "max_workers": int(os.getenv("MAX_WORKERS", "4")),
            "default_jurisdiction": "tx",
            "allowed_jurisdictions": os.getenv("ALLOWED_JURISDICTIONS", "tx,ca,fed").split(","),
            **(config or {})
        }
        
        # User information for access control
        self.user_id = user_id
        self.user_role = user_role
        self.tenant_id = tenant_id
        
        # Role-based jurisdiction access mapping
        self.role_jurisdiction_map = {
            "partner": self.config["allowed_jurisdictions"],  # Partners get access to all jurisdictions
            "attorney": self.config["allowed_jurisdictions"],  # Attorneys get access to all jurisdictions
            "paralegal": ["tx"],  # Paralegals get limited access by default
            "staff": ["tx"],  # Staff get limited access by default
            "client": []  # Clients get access based on their cases only
        }
        
        # Initialize connectors (lazy-loaded)
        self._supabase = None
        self._gcs = None
        self._pinecone = None
        self._neo4j = None
        self._neo4j_init_attempted = False # Flag to track initialization attempt
        self._neo4j_lock = Lock() # Lock for neo4j initialization
        
        # Ensure database tables exist
        from src.processing.storage.supabase_connector import SupabaseConnector
        SupabaseConnector(ensure_tables_exist=True)
        
        # Initialize pipeline components
        self._court_listener = None
        self._findlaw = None
        self._citation_extractor = None
        
        # Track the current processing batch
        self.current_batch_id = None
        self.current_batch_stats = {
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0
        }
        
        # Role-based jurisdiction access control
        self.role_jurisdiction_map = {
            "partner": self.config["allowed_jurisdictions"],  # Partners have access to all jurisdictions
            "attorney": ["tx", "ca", "fed"],
            "paralegal": ["tx", "ca"],
            "staff": ["tx"],
            "client": ["tx"]  # Clients only see Texas cases
        }
        
        logger.info(f"Case Law Processor initialized with config: {self.config}")
        if user_id and user_role:
            logger.info(f"Access control enabled for user {user_id} with role {user_role}")
    
    @property
    def supabase(self):
        """Lazy-loaded Supabase connection"""
        if self._supabase is None:
            from src.processing.storage.supabase_connector import SupabaseConnector
            self._supabase = SupabaseConnector()
        return self._supabase
    
    @property
    def gcs(self):
        """Lazy-loaded Google Cloud Storage connection"""
        if self._gcs is None:
            from src.processing.storage.gcs_connector import GCSConnector
            self._gcs = GCSConnector(bucket_name=self.config["gcs_bucket"])
        return self._gcs
        
    @property
    def pinecone(self):
        """Lazy-loaded Pinecone vector store connection"""
        if self._pinecone is None:
            from src.processing.storage.vector_store import PineconeConnector
            self._pinecone = PineconeConnector(index_name=self.config["pinecone_index"])
        return self._pinecone
    
    @property
    def neo4j(self):
        """Lazy-loaded and thread-safe Neo4j graph database connection"""
        # Quick check without lock first
        if self._neo4j_init_attempted:
            return self._neo4j
        
        # Acquire lock to ensure only one thread initializes
        with self._neo4j_lock:
            # Double-check if another thread initialized while waiting for the lock
            if self._neo4j_init_attempted:
                return self._neo4j
            
            # Mark that we are attempting initialization NOW
            self._neo4j_init_attempted = True
            logger.info("Lazily initializing Neo4j connector (inside lock)...")
            try:
                # Attempt to instantiate the connector
                connector_instance = Neo4jConnector()
                
                # Check if the driver connection failed internally during init
                if not hasattr(connector_instance, '_driver') or connector_instance._driver is None:
                    logger.warning("Neo4jConnector initialized but failed to establish a driver connection. Neo4j operations will be skipped.")
                    self._neo4j = connector_instance
                else:
                    logger.info("Neo4jConnector initialized successfully.")
                    self._neo4j = connector_instance
            
            except Exception as e:
                logger.error(f"Failed to initialize Neo4jConnector: {repr(e)}")
                logger.exception("Traceback for Neo4jConnector initialization error:")
                self._neo4j = None # Ensure self._neo4j is None if init fails completely
            
            return self._neo4j
    
    @property
    def court_listener(self):
        """Lazy-loaded Court Listener API client"""
        if self._court_listener is None:
            from src.processing.providers.court_listener import CourtListenerConnector
            self._court_listener = CourtListenerConnector()
        return self._court_listener
        
    @property
    def findlaw(self):
        """Lazy-loaded FindLaw API client"""
        if self._findlaw is None:
            from src.processing.providers.findlaw import FindLawConnector
            self._findlaw = FindLawConnector()
        return self._findlaw
        
    @property
    def citation_extractor(self):
        """Lazy-loaded citation extractor"""
        if self._citation_extractor is None:
            from src.processing.citation_extractor import CitationExtractor
            # Use Court Listener API for citation extraction if available
            use_api = hasattr(self.court_listener, 'api_key') and self.court_listener.api_key is not None
            self._citation_extractor = CitationExtractor(
                use_court_listener_api=use_api,
                api_key=getattr(self.court_listener, 'api_key', None)
            )
        return self._citation_extractor
    
    def get_allowed_jurisdictions(self) -> List[str]:
        """
        Get the list of jurisdictions the current user has access to based on their role.
        
        Role-based access rules:
        - partner: All jurisdictions
        - attorney: All jurisdictions
        - paralegal: Jurisdictions specified in their tenant settings
        - staff: Jurisdictions specified in their tenant settings
        - client: Only their specific case jurisdictions
        
        Returns:
            List of jurisdiction codes the user can access
        """
        if not self.user_role:
            # Default to all jurisdictions if no role specified (system process)
            return self.config["allowed_jurisdictions"]
        
        # Admin roles get access to all jurisdictions
        if self.user_role in ["partner", "attorney"]:
            return self.config["allowed_jurisdictions"]
            
        # Staff roles get access to jurisdictions based on tenant settings
        if self.user_role in ["paralegal", "staff"]:
            # If tenant_id is provided, get jurisdictions for that tenant
            if self.tenant_id:
                tenant_jurisdictions = self.supabase.get_tenant_jurisdictions(self.tenant_id)
                if tenant_jurisdictions:
                    return tenant_jurisdictions
            # Fallback to role map if tenant-specific settings not found
            return self.role_jurisdiction_map.get(self.user_role, [])
            
        # Clients only get access to jurisdictions of their own cases
        if self.user_role == "client" and self.user_id:
            client_jurisdictions = self.supabase.get_client_case_jurisdictions(self.user_id)
            if client_jurisdictions:
                return client_jurisdictions
            # If no cases found, return empty list (no access)
            return []
            
        # Default fallback to role-jurisdiction map
        return self.role_jurisdiction_map.get(self.user_role, [])
    
    def check_jurisdiction_access(self, jurisdiction: str) -> bool:
        """
        Check if the current user has access to the specified jurisdiction.
        
        Args:
            jurisdiction: The jurisdiction code to check
            
        Returns:
            Boolean indicating if access is allowed
        """
        if not self.user_role:
            # Default to allowed if no role specified
            return True
            
        allowed_jurisdictions = self.get_allowed_jurisdictions()
        return jurisdiction in allowed_jurisdictions
    
    def filter_results_by_jurisdiction(self, results: List[Dict]) -> List[Dict]:
        """
        Filter a list of results to only include those from allowed jurisdictions.
        
        Args:
            results: List of result dictionaries with jurisdiction field
            
        Returns:
            Filtered list of results
        """
        if not self.user_role:
            return results
            
        allowed_jurisdictions = self.get_allowed_jurisdictions()
        return [r for r in results if r.get('jurisdiction', '') in allowed_jurisdictions]
    
    def start_batch(self, source: str, jurisdiction: str, query_params: Optional[Dict] = None) -> str:
        """
        Start a new processing batch.
        
        Args:
            source: The data source (e.g., "court_listener", "find_law")
            jurisdiction: The jurisdiction code (e.g., "tx", "ca")
            query_params: Optional parameters for the data source query
            
        Returns:
            batch_id: A unique identifier for this processing batch
        """
        self.current_batch_id = str(uuid.uuid4())
        self.current_batch_stats = {
            "batch_id": self.current_batch_id,
            "source": source,
            "jurisdiction": jurisdiction,
            "start_time": datetime.now().isoformat(),
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "status": "processing",
            "query_params": query_params or {}
        }
        
        logger.info(f"Started new batch {self.current_batch_id} for {jurisdiction} from {source}")
        
        # Register batch in database for tracking
        self.supabase.create_processing_batch(self.current_batch_stats)
        
        return self.current_batch_id
    
    def process_jurisdiction(self, jurisdiction: str, query: str = "", count: int = 100, source: str = "court_listener") -> Dict:
        """
        Process cases for a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tx", "ca")
            query: Search query for filtering cases
            count: Maximum number of cases to process
            source: Data source to use ("court_listener" or "findlaw")
            
        Returns:
            Dict with processing statistics or error message
        """
        # Check jurisdiction access
        if not self.check_jurisdiction_access(jurisdiction):
            error_msg = f"Access denied for jurisdiction {jurisdiction} with role {self.user_role}"
            logger.warning(error_msg)
            return {
                "status": "error",
                "error": "Access denied",
                "message": error_msg,
                "jurisdiction": jurisdiction
            }
        
        # Start a new batch
        batch_id = self.start_batch(source, jurisdiction, {"query": query, "count": count})
        
        try:
            # Get cases from the appropriate data source
            search_results = {"results": []}
            successful_case_ids = [] # Initialize list to store successful IDs
            if source == "court_listener":
                search_results = self.court_listener.search_opinions(
                    query=query,
                    jurisdiction=jurisdiction,
                    per_page=count
                )
            elif source == "findlaw":
                search_results = self.findlaw.search_opinions(
                    query=query,
                    jurisdiction=jurisdiction,
                    page=1,
                    per_page=count
                )
            else:
                raise ValueError(f"Unknown source: {source}")
            
            total_cases = len(search_results.get("results", []))
            self.current_batch_stats["total"] = total_cases
            logger.info(f"Found {total_cases} cases for jurisdiction {jurisdiction} from {source}")
            
            # Process cases in parallel if configured
            if self.config["max_workers"] > 1 and total_cases > 1:
                with ThreadPoolExecutor(max_workers=self.config["max_workers"]) as executor:
                    # Submit all cases for processing
                    futures = {executor.submit(self._process_case_safe, case_data, jurisdiction): case_data for case_data in search_results.get("results", [])}
                    
                    # Process results as they complete
                    for future in as_completed(futures):
                        case_data = futures[future]
                        case_id_str = str(case_data.get('id', 'unknown_during_completion'))
                        try:
                            result_status = future.result()
                            # **** ADDED LOGGING ****
                            logger.debug(f"Future completed for case {case_id_str}. Result status: '{result_status}' (Type: {type(result_status)}) ")
                        
                            if result_status == 'success':
                                # **** ADDED LOGGING ****
                                logger.debug(f"Case {case_id_str} matched 'success'. Incrementing success count.")
                                self.current_batch_stats["success"] += 1
                                successful_case_ids.append(case_id_str) # Add successful ID
                            elif result_status == 'failure':
                                # **** ADDED LOGGING ****
                                logger.debug(f"Case {case_id_str} matched 'failure'. Incrementing failure count.")
                                self.current_batch_stats["failure"] += 1
                            elif result_status == 'skipped':
                                # **** ADDED LOGGING ****
                                logger.debug(f"Case {case_id_str} matched 'skipped'. Incrementing skipped count.")
                                self.current_batch_stats["skipped"] += 1
                            else:
                                # **** ADDED LOGGING ****
                                logger.warning(f"Case {case_id_str}: Unhandled result status '{result_status}'. Not incrementing counts.")
                            
                        except Exception as exc:
                            logger.error(f'Case {case_id_str} generated an exception during future processing: {exc}')
                            logger.exception(f"Traceback for future exception on case {case_id_str}:")
                            self.current_batch_stats["failure"] += 1 # Count exceptions during future retrieval as failures
                            # Attempt to log the error in Supabase history and error tables directly from here if needed
                            # Note: This might be redundant if _process_case_safe already logged it.
                            try:
                                # We need access to the Supabase connector here, assuming self.supabase is initialized
                                if self.supabase:
                                    error_message = f"Future processing error: {repr(exc)}"
                                    self.supabase.log_processing_error(batch_id, case_id_str, error_message, traceback.format_exc())
                                    self.supabase.log_processing_history(batch_id, case_id_str, 'failure', error_message)
                                else:
                                    logger.error("Supabase connector not initialized, cannot log future exception to DB.")
                            except Exception as log_exc:
                                 logger.error(f"Failed to log future exception for case {case_id_str} to Supabase: {repr(log_exc)}")

            else:
                # Process sequentially
                for case_data in search_results.get("results", []):
                    case_result = self.process_case(case_data, jurisdiction)
                    case_id = case_data.get("id") # Get case ID
                    
                    # Update stats
                    if case_result["status"] == "success":
                        self.current_batch_stats["success"] += 1
                        if case_id: successful_case_ids.append(str(case_id)) # Add successful ID
                    elif case_result["status"] == "skipped":
                        self.current_batch_stats["skipped"] += 1
                    else:
                        self.current_batch_stats["failure"] += 1
            
            # Complete the batch
            self.complete_batch(success=True)
            
        except Exception as e:
            logger.error(f"Error processing jurisdiction {jurisdiction}: {str(e)}")
            self.complete_batch(success=False, error=str(e))
        
        # Complete the batch normally after processing
        self.complete_batch("completed")
        final_stats = self.current_batch_stats.copy()
        final_stats["processed_case_ids"] = successful_case_ids # Add list to final stats
        logger.info(f"Batch stats: {final_stats}")
        return final_stats # Return updated stats
        
    def _process_case_safe(self, case_data: Dict, jurisdiction: str) -> str:
        """
        Wrapper for process_case that handles exceptions for parallel processing.
        
        Args:
            case_data: Case data
            jurisdiction: Jurisdiction code
            
        Returns:
            Processing result string
        """
        # Ensure case_id is extracted correctly at the start
        case_id_str = str(case_data.get('id', 'unknown_in_safe_process')) 
        try:
            # Check if the case already exists and should be skipped
            # Note: We need a way to get the batch_id here if skipping logic depends on it.
            # Simplified check: Assume skipping logic is handled elsewhere or not needed here.
            # if self.supabase.check_case_exists(case_id_str): 
            #    logger.info(f"Skipping already processed case {case_id_str}")
            #    return 'skipped' 
            
            # Pass jurisdiction from the outer scope
            processing_result = self.process_case(case_data, jurisdiction)
            
            # Check the result from process_case
            if processing_result is None:
                logger.warning(f"_process_case_safe: process_case returned None for case {case_id_str}, indicating failure.")
                return 'failure' # Explicitly return 'failure' status string
            else:
                # Assuming process_case returns a dict on success, we return 'success' status
                logger.info(f"_process_case_safe: Successfully processed case {case_id_str}")
                return 'success'
            
        except Exception as e:
            # This except block catches errors *directly* within _process_case_safe
            # Errors caught *within* process_case are handled by its own except block.
            logger.error(f"Error in _process_case_safe for case {case_id_str}: {repr(e)}")
            logger.exception(f"Traceback for error in _process_case_safe {case_id_str}:")
            
            # We return None to indicate failure within this specific method
            return 'failure'
    
    def process_case(self, case_data: Dict, jurisdiction: str) -> Optional[Dict]:
        """
        Process a single case through the entire pipeline.
        
        Args:
            case_data: Case data from Court Listener API
            jurisdiction: Jurisdiction code
            
        Returns:
            Dict with processing result or None on failure
        """
        case_id = case_data.get("id") or case_data.get("cluster_id") or case_data.get("source_id")
        if not case_id:
            case_id = str(uuid.uuid4())  # Generate ID if none exists
            case_data["id"] = case_id
        
        # Check jurisdiction access
        if not self.check_jurisdiction_access(jurisdiction):
            logger.warning(f"Access denied for case {case_id} in jurisdiction {jurisdiction} with role {self.user_role}")
            return {
                "status": "error",
                "error": "Access denied",
                "case_id": case_id,
                "jurisdiction": jurisdiction
            }
        
        # Early return if we've already processed this case with good quality
        if self.is_case_already_processed(case_id, min_quality=0.7):
            logger.info(f"Case {case_id} already processed with good quality, skipping")
            return {"status": "skipped", "case_id": case_id, "reason": "already_processed"}
        
        try:
            # Step 1: Validate and normalize case data
            logger.info(f"Processing case {case_id} ({case_data.get('case_name', 'Unknown')})")
            case_data["jurisdiction"] = jurisdiction
            
            # Record user who initiated processing if available
            if self.user_id:
                case_data["processed_by"] = self.user_id
                case_data["processed_role"] = self.user_role
            
            # Step 2: Store case metadata in Supabase
            logger.info(f"Storing case {case_id} metadata in Supabase")
            if not self.current_batch_id:
                 logger.error(f"Critical error: current_batch_id is None during process_case for case {case_id}. Cannot proceed.")
                 # Decide how to handle this - raise exception or return None?
                 # Raising exception might be better to halt processing if batch context is lost.
                 raise ValueError(f"Missing batch_id for case {case_id}")
                 # return None # Alternative: treat as failure
            
            stored_case = self.supabase.store_case(case_data, batch_id=self.current_batch_id)
            
            # Step 2.5: Ensure Case node exists in Neo4j
            neo4j_properties = {
                'name': case_data.get("caseName", ""),
                'jurisdiction': case_data.get("jurisdiction", "unknown"),
                'date_filed': case_data.get("dateFiled"),
                'court_id': case_data.get("court_id")
                # Add other relevant properties as needed
            }
            # Use MERGE logic in create_case_node, so safe to call even if node exists
            create_node_success = self.neo4j.create_case_node(case_id, neo4j_properties)
            if not create_node_success:
                # Log warning but continue processing, as relationship creation might still work if node exists
                logger.warning(f"Failed to explicitly create/merge Neo4j node for case {case_id}, but proceeding.")
            else:
                logger.info(f"Ensured Neo4j node exists for case {case_id}")

            # Step 3: Process opinions
            opinion_results = []
            for opinion_data in case_data.get("opinions", []):
                opinion_result = self.process_opinion(opinion_data, case_id, jurisdiction)
                opinion_results.append(opinion_result)
            
            # Step 4: Process citations using our citation extractor
            all_text = ""
            for opinion in opinion_results:
                if opinion.get("text"):
                    all_text += opinion.get("text") + "\n\n"
            
            # Extract citations from the combined text
            citations = []
            if all_text:
                citations = self.citation_extractor.extract_cited_cases(case_id, all_text, jurisdiction)
            
            # Step 5: Store citations in Neo4j knowledge graph
            citation_results = self.process_citations(case_data, opinion_results, citations)
            
            # Step 6: Update case with processing results and quality metrics
            # Find the GCS path from the first successfully processed opinion
            first_opinion_gcs_path = None
            for opinion_result in opinion_results:
                if opinion_result.get('status') == 'success' and opinion_result.get('gcs_path'):
                    first_opinion_gcs_path = opinion_result['gcs_path']
                    break # Found the first one
            
            # Update main case metadata, including gcs_path if found
            logger.info(f"Case {case_id}: Found first opinion GCS path: {first_opinion_gcs_path}")
            case_update_data = {}
            if first_opinion_gcs_path:
                case_update_data['gcs_path'] = first_opinion_gcs_path
            else:
                 logger.warning(f"No successful opinion with GCS path found for case {case_id} to update main record.")
            # Potentially add other metadata updates here if needed in the future
            
            if case_update_data:
                logger.info(f"Case {case_id}: Attempting to update case metadata with: {case_update_data}")
                update_success = self.supabase.update_case_metadata(case_id, case_update_data)
                logger.info(f"Case {case_id}: update_case_metadata result: {update_success}")
            
            # Update case stats (opinion count, citation count, quality)
            quality_metrics = self._calculate_quality_metrics(opinion_results)
            self.supabase.update_case_stats(
                case_id=case_id,
                opinion_count=len(opinion_results),
                citation_count=len(citation_results),
                quality_metrics=quality_metrics
            )
            
            # Check if any opinions were successfully processed (meaning text content was found and processed)
            if len([r for r in opinion_results if r.get('status') == 'success']) > 0:
                logger.info(f"Finished processing case {case_id} with {len([r for r in opinion_results if r.get('status') == 'success'])} successful opinions.")
                # Log history for successful case processing
                self.supabase.log_processing_history(
                    batch_id=self.current_batch_id,
                    case_id=case_id,
                    status='success',
                    message=f"Processed {len([r for r in opinion_results if r.get('status') == 'success'])} opinions successfully."
                )
                return {
                    "status": "success",
                    "case_id": case_id,
                    "opinions": len(opinion_results),
                    "citations": len(citation_results),
                    "quality": quality_metrics,
                    "jurisdiction": jurisdiction
                }
            else:
                # No opinions successfully processed (likely due to no text content)
                logger.warning(f"Finished processing case {case_id}, but no opinions had sufficient text content to process. Marking case as failed.")
                # Log this specific failure reason to Supabase history
                try:
                    if self.supabase and self.current_batch_id:
                        self.supabase.log_processing_history(
                            batch_id=self.current_batch_id,
                            case_id=case_id,
                            status='failure', # Indicate failure at case level
                            message="No opinions with sufficient text content found."
                        )
                except Exception as log_exc:
                    logger.error(f"Failed to log 'no sufficient text' failure for case {case_id}: {log_exc}")
                return None # Return None to indicate failure at the case level
            
        except Exception as e:
            logger.error(f"Error processing case {case_id}: {repr(e)}")
            logger.debug(f"Case data that caused error in process_case: {case_data}") 
            # Log traceback for the error within process_case
            logger.exception(f"Traceback for error in process_case {case_id}:")
            
            # We return None to indicate failure within this specific method
            return None # Match return type hint expected by _process_case_safe
    
    def process_opinion(self, opinion_data: Dict, case_id: str, jurisdiction: str) -> Dict:
        """
        Process a single opinion document through the pipeline.
        
        Args:
            opinion_data: Opinion data from Court Listener API
            case_id: The parent case ID
            jurisdiction: Jurisdiction code
            
        Returns:
            Dict with processing result
        """
        opinion_id = opinion_data.get("id")
        logger.info(f"Processing opinion {opinion_id} from case {case_id}")
        
        try:
            # Step 1: Get best text content
            text_content, text_format = self._get_best_text_content(opinion_data)
            
            if not text_content:
                logger.info(f"Opinion {opinion_id} for case {case_id} has no plain_text. Skipping GCS, Pinecone, and further processing for this opinion.")
                return {'status': 'skipped', 'opinion_id': opinion_id, 'reason': 'No plain_text'}
            
            # Step 2: Store opinion in GCS
            gcs_path = f"cases/{jurisdiction}/{case_id}/opinions/{opinion_id}.txt"
            logger.info(f"Opinion {opinion_id}: Attempting to store text in GCS at {gcs_path}")
            self.gcs.store_text(text_content, gcs_path)
            logger.info(f"Opinion {opinion_id}: Successfully stored text in GCS.")
            
            # Step 3: Store opinion metadata in Supabase
            opinion_record = {
                "id": opinion_id,
                "case_id": case_id,
                "author_str": opinion_data.get("author_str"),
                "type": opinion_data.get("type"),
                "sha1": opinion_data.get("sha1"),
                "gcs_path": gcs_path,
                "text_format": text_format,
                "has_full_text": True,
                "extracted_text_length": len(text_content),
                "page_count": opinion_data.get("page_count")
            }
            
            # Store opinion, passing the current batch_id for logging
            stored_opinion = self.supabase.store_opinion(opinion_record, batch_id=self.current_batch_id)
            
            # Step 4: Process opinion chunks for vector storage
            chunks = self._chunk_text(text_content)
            chunk_results = []
            
            for i, chunk_text in enumerate(chunks):
                # Store chunk in Supabase
                chunk_id = str(uuid.uuid4())
                chunk_record = {
                    "id": chunk_id,
                    "opinion_id": opinion_id,
                    "case_id": case_id,
                    "jurisdiction": jurisdiction,
                    "chunk_index": i,
                    "chunk_text": chunk_text,
                    "token_count": len(chunk_text.split())
                }
                
                self.supabase.store_opinion_chunk(chunk_record)
                
                # Generate embedding and store in Pinecone
                logger.info(f"Opinion {opinion_id}, Chunk {i}: Attempting to generate embedding.")
                embedding = self.pinecone.generate_embedding(chunk_text)
                logger.info(f"Opinion {opinion_id}, Chunk {i}: Successfully generated embedding.")
                metadata = {
                    "case_id": case_id,
                    "opinion_id": opinion_id,
                    "chunk_id": chunk_id,
                    "jurisdiction": jurisdiction,
                    "chunk_index": i,
                    "text_length": len(chunk_text),
                    "case_name": opinion_data.get("case_name", "")
                }
                
                vector_id = f"{jurisdiction}_{case_id}_{opinion_id}_{i}"
                logger.info(f"Upserting vector to Pinecone: ID={vector_id}, Metadata={metadata}")
                self.pinecone.upsert_vector(vector_id, embedding, metadata)
                # Add logging here if upsert_vector returned a status, currently it doesn't
                
                # Update chunk with vector ID
                self.supabase.update_chunk_vector_id(chunk_id, vector_id)
                
                chunk_results.append({"chunk_id": chunk_id, "vector_id": vector_id})
            
            # Return results
            logger.info(f"Successfully processed opinion {opinion_id} for case {case_id}")
            return {
                "status": "success",
                "opinion_id": opinion_id,
                "gcs_path": gcs_path,
                "chunks": len(chunks),
                "quality": 1.0 if len(chunks) > 0 else 0.5,
                "has_text": True
            }
            
        except Exception as e:
            logger.exception(f"Error processing opinion {opinion_id} for case {case_id}") 
            # Log the error in Supabase
            self.supabase.log_processing_error(
                case_id=case_id,
                opinion_id=opinion_id,
                batch_id=self.current_batch_id,
                error=str(e),
                error_context={"stage": "opinion_processing"}
            )
            
            return {
                "status": "failure",
                "opinion_id": opinion_id,
                "error": str(e),
                "quality": 0,
                "has_text": False
            }
    
    def process_citations(self, case_data: Dict, opinion_results: List[Dict], extracted_citations: Optional[List[Dict]] = None) -> List[Dict]:
        """
        Process citations for a case.
        
        Args:
            case_data: Case data from Court Listener API or other source
            opinion_results: Results from opinion processing
            extracted_citations: Optional pre-extracted citations from the citation extractor
            
        Returns:
            List of processed citations
        """
        case_id = case_data.get("id") or case_data.get("cluster_id") or case_data.get("source_id")
        jurisdiction = case_data.get("jurisdiction")
        logger.info(f"Processing citations for case {case_id} in {jurisdiction}")
        
        # Check jurisdiction access
        if not self.check_jurisdiction_access(jurisdiction):
            logger.warning(f"Access denied for citations in jurisdiction {jurisdiction} with role {self.user_role}")
            return []
            
        # Initialize citation results list
        citation_results = []
        
        # Process pre-extracted citations if provided
        if extracted_citations:
            logger.info(f"Processing {len(extracted_citations)} pre-extracted citations")
            for citation in extracted_citations:
                try:
                    # Add citation metadata
                    citation_id = citation.get("id") or str(uuid.uuid4())
                    citation_record = {
                        "id": citation_id,
                        "citing_case_id": case_id,
                        "cited_case_id": citation.get("cited_id"),
                        "citation_text": citation.get("text"),
                        "confidence": citation.get("confidence", 0.8),
                        "jurisdiction": jurisdiction,
                        "extracted_by": "citation_extractor"
                    }
                    
                    # Store in Supabase
                    self.supabase.store_citation(citation_record)
                    
                    # Add to Neo4j knowledge graph
                    self.neo4j.add_citation(
                        citing_id=case_id,
                        cited_id=citation.get("cited_id"),
                        citation_text=citation.get("text"),
                        jurisdiction=jurisdiction,
                        confidence=citation.get("confidence", 0.8)
                    )
                    
                    citation_results.append(citation_record)
                except Exception as e:
                    logger.warning(f"Error storing extracted citation: {str(e)}")
                    continue
        
        # Also process citations from the original API data if available
        for opinion_data in case_data.get("opinions", []):
            opinion_id = opinion_data.get("id")
            
            # Extract citations from opinions_cited field
            for cited_opinion in opinion_data.get("opinions_cited", []):
                cited_id = cited_opinion.get("id") or cited_opinion.get("cluster_id")
                
                if cited_id and cited_id != opinion_id:
                    # Check if this citation was already processed from extracted_citations
                    if any(c.get("cited_case_id") == cited_id for c in citation_results):
                        continue
                        
                    citation = {
                        "id": str(uuid.uuid4()),
                        "citing_case_id": case_id,
                        "cited_case_id": cited_id,
                        "opinion_id": opinion_id,
                        "citation_text": cited_opinion.get("citation", ""),
                        "confidence": 1.0,  # Direct citation from API
                        "jurisdiction": jurisdiction,
                        "extracted_by": "api"
                    }
                    
                    # Store in Supabase
                    try:
                        self.supabase.store_citation(citation)
                        
                        # Add to Neo4j graph
                        self.neo4j.add_citation(
                            citing_id=case_id,
                            cited_id=cited_id,
                            citation_text=cited_opinion.get("citation", ""),
                            jurisdiction=jurisdiction,
                            confidence=1.0
                        )
                        
                        citation_results.append(citation)
                        
                    except Exception as e:
                        logger.warning(f"Error storing citation: {str(e)}")
                        continue
        
        return citation_results
    
    def complete_batch(self, success: bool = True, error: Optional[str] = None) -> Dict:
        """
        Mark the current batch as complete.
        
        Args:
            success: Whether the batch was successful
            error: Optional error message if batch failed
            
        Returns:
            Dict with batch statistics
        """
        if not self.current_batch_id:
            return {}
        
        self.current_batch_stats["status"] = "completed" if success else "failed"
        self.current_batch_stats["end_time"] = datetime.now().isoformat()
        
        if error:
            self.current_batch_stats["error"] = error
        
        # Update batch in Supabase
        self.supabase.update_processing_batch(self.current_batch_id, self.current_batch_stats)
        
        logger.info(f"Completed batch {self.current_batch_id} with status: {self.current_batch_stats['status']}")
        logger.info(f"Batch stats: {self.current_batch_stats}")
        
        return self.current_batch_stats
    
    def is_case_already_processed(self, case_id: str, min_quality: float = 0.5) -> bool:
        """
        Check if a case has already been processed with sufficient quality.
        
        Args:
            case_id: The case ID to check
            min_quality: Minimum quality score to consider sufficient
            
        Returns:
            Boolean indicating if case should be skipped
        """
        case = self.supabase.get_case(case_id)
        
        if not case:
            return False
        
        # Check if we have sufficient quality
        quality_score = case.get("completeness_score", 0)
        return quality_score >= min_quality
    
    def _get_best_text_content(self, opinion_data: Dict) -> tuple:
        """
        Extract the best available text content from an opinion.
        
        Args:
            opinion_data: Opinion data from Court Listener API
            
        Returns:
            Tuple of (text_content, format_name)
        """
        # Priority order for text fields based on Court Listener Docs (best to worst)
        text_fields = [
            ("html_with_citations", "html_with_citations"),
            ("html_columbia", "html_columbia"),
            ("html_lawbox", "html_lawbox"),
            ("xml_harvard", "xml_harvard"),
            ("html_anon_2020", "html_anon_2020"),
            ("html", "html"),
            ("plain_text", "plain_text")
        ]
        
        for field_name, format_name in text_fields:
            logger.debug(f"Checking field: {field_name}")
            if field_name in opinion_data and opinion_data[field_name]:
                content = opinion_data[field_name]
                try:
                    if content and len(content) > 100:  # Ensure minimum useful content
                        logger.debug(f"Using content from field: {field_name}")
                        return content, format_name
                    else:
                        logger.debug(f"Field {field_name} exists but content is too short or empty.")
                except TypeError as te:
                    # This might happen if content is not a string/bytes/collection
                    logger.warning(f"Type error checking length of field '{field_name}' for opinion. Content type: {type(content)}. Error: {te}")
                except Exception as e:
                    logger.error(f"Unexpected error checking field '{field_name}': {e}")
        
        logger.debug("No suitable text field found with sufficient content.")
        return "", ""
    
    def _chunk_text(self, text: str, max_chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Split text into overlapping chunks for vector embedding.
        
        Args:
            text: The full text to chunk
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks
        """
        chunks = []
        start = 0
        
        # Simple chunking by characters with overlap
        while start < len(text):
            end = min(start + max_chunk_size, len(text))
            
            # Try to end at sentence or paragraph boundary if possible
            if end < len(text):
                for boundary in ["\n\n", "\n", ". ", "? ", "! "]:
                    boundary_pos = text.rfind(boundary, start, end)
                    if boundary_pos > start + max_chunk_size // 2:  # Ensure minimum chunk size
                        end = boundary_pos + len(boundary)
                        break
            
            chunks.append(text[start:end])
            start = end - overlap
        
        return chunks
    
    def _calculate_quality_metrics(self, opinion_results: List[Dict]) -> Dict:
        """
        Calculate quality metrics for a case based on opinion processing results.
        
        Args:
            opinion_results: List of opinion processing results
            
        Returns:
            Dict with quality metrics
        """
        total_opinions = len(opinion_results)
        if total_opinions == 0:
            return {
                "completeness_score": 0,
                "document_quality": "Poor",
                "metadata_quality": "Poor"
            }
        
        # Calculate success rate
        success_count = sum(1 for result in opinion_results if result.get("status") == "success")
        has_text_count = sum(1 for result in opinion_results if result.get("has_text", False))
        
        completeness_score = has_text_count / total_opinions
        
        # Determine quality labels
        if completeness_score >= 0.8:
            document_quality = "Good"
        elif completeness_score >= 0.5:
            document_quality = "Fair"
        else:
            document_quality = "Poor"
        
        return {
            "completeness_score": completeness_score,
            "document_quality": document_quality,
            "metadata_quality": "Good" if success_count == total_opinions else "Fair"
        }


def process_jurisdiction_cases(jurisdiction: str, query: str = "", count: int = 100) -> Dict:
    """
    Process cases for a specific jurisdiction.
    
    Args:
        jurisdiction: Jurisdiction code (e.g., "tx", "ca")
        query: Search query for filtering cases
        count: Maximum number of cases to process
        
    Returns:
        Dict with processing statistics
    """
    processor = CaseLawProcessor()
    return processor.process_jurisdiction(jurisdiction, query, count)


if __name__ == "__main__":
    import sys
    
    jurisdiction = "tx"
    query = ""
    count = 20
    
    if len(sys.argv) > 1:
        jurisdiction = sys.argv[1]
    
    if len(sys.argv) > 2:
        query = sys.argv[2]
    
    if len(sys.argv) > 3:
        count = int(sys.argv[3])
    
    result = process_jurisdiction_cases(jurisdiction, query, count)
    
    print(f"\n===== CASE LAW PROCESSING COMPLETE =====")
    print(f"Batch ID: {result['batch_id']}")
    print(f"Jurisdiction: {result['jurisdiction']}")
    print(f"Total Cases: {result['total']}")
    print(f"Successful: {result['success']}")
    print(f"Failed: {result['failure']}")
    print(f"Skipped: {result['skipped']}")
    print(f"Success Rate: {(result['success'] / result['total']) * 100:.2f}% (excluding skipped)")
