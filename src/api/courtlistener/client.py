"""
Court Listener API Client
Handles communication with the Court Listener REST API
"""

import os
import json
import time
import logging
import requests
from typing import Dict, List, Optional, Union, Any
from urllib.parse import urljoin
from dotenv import load_dotenv

from .exceptions import (
    CourtListenerAPIError,
    CourtListenerAuthenticationError,
    CourtListenerRateLimitError,
    CourtListenerResourceNotFoundError
)
from .models import Case, Opinion, Court

# Configure logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


class CourtListenerClient:
    """Client for interacting with the Court Listener API"""
    
    BASE_URL = "https://www.courtlistener.com/api/rest/v4/"
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Court Listener API client
        
        Args:
            api_key: API key for Court Listener. If not provided, it will be loaded from COURTLISTENER_API_KEY
                    environment variable.
        
        Raises:
            ValueError: If no API key is provided and none is found in the environment
        """
        self.api_key = api_key or os.getenv("COURTLISTENER_API_KEY")
        if not self.api_key:
            raise ValueError("Court Listener API key not found")
        
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def _make_request(self, endpoint: str, method: str = "GET", params: Optional[Dict] = None, 
                     data: Optional[Dict] = None, retries: int = 3, backoff_factor: float = 0.5) -> Dict:
        """
        Make a request to the Court Listener API with retry logic
        
        Args:
            endpoint: API endpoint (will be appended to BASE_URL)
            method: HTTP method (GET, POST, etc.)
            params: URL parameters
            data: Request body for POST/PUT
            retries: Number of retries on failure
            backoff_factor: Backoff factor for retries
            
        Returns:
            JSON response from the API
            
        Raises:
            CourtListenerAPIError: For general API errors
            CourtListenerAuthenticationError: For authentication errors
            CourtListenerRateLimitError: When rate limit is exceeded
            CourtListenerResourceNotFoundError: When resource is not found
        """
        url = urljoin(self.BASE_URL, endpoint)
        
        for attempt in range(retries):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data
                )
                
                # Handle different status codes
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 401:
                    raise CourtListenerAuthenticationError(
                        message="Authentication failed with Court Listener API", 
                        status_code=response.status_code, 
                        response=response.text
                    )
                elif response.status_code == 404:
                    raise CourtListenerResourceNotFoundError(
                        message=f"Resource not found: {endpoint}", 
                        status_code=response.status_code, 
                        response=response.text
                    )
                elif response.status_code == 429:
                    if attempt < retries - 1:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.warning(f"Rate limit hit, waiting {wait_time}s before retry...")
                        time.sleep(wait_time)
                        continue
                    raise CourtListenerRateLimitError(
                        message="Rate limit exceeded for Court Listener API", 
                        status_code=response.status_code, 
                        response=response.text
                    )
                else:
                    # Handle other errors
                    raise CourtListenerAPIError(
                        message=f"API Error: {response.text}", 
                        status_code=response.status_code, 
                        response=response.text
                    )
            
            except (requests.RequestException, ConnectionError) as e:
                # Network-related errors
                if attempt < retries - 1:
                    wait_time = backoff_factor * (2 ** attempt)
                    logger.warning(f"Connection error, retrying in {wait_time}s: {str(e)}")
                    time.sleep(wait_time)
                else:
                    raise CourtListenerAPIError(f"Connection failed after {retries} retries: {str(e)}")
    
    def search_cases(self, query: Optional[str] = None, citation: Optional[str] = None, 
                    docket_number: Optional[str] = None, case_name: Optional[str] = None,
                    jurisdiction: Optional[str] = None, court: Optional[str] = None,
                    judge: Optional[str] = None, status: Optional[str] = None,
                    page: int = 1, page_size: int = 20, **kwargs) -> Dict:
        """
        Search for cases using various criteria
        
        Args:
            query: Full-text search query
            citation: Case citation (e.g., "410 U.S. 113")
            docket_number: Docket number
            case_name: Name of the case
            jurisdiction: Jurisdiction code (e.g., "tex" for Texas)
            court: Court ID or abbreviation
            judge: Judge name
            status: Precedential status
            page: Page number for pagination
            page_size: Number of results per page
            **kwargs: Additional search parameters
            
        Returns:
            Search results with pagination information
        """
        endpoint = "search/"
        params = {
            "format": "json",
            "page": page,
            "page_size": page_size
        }
        
        # Add optional parameters if provided
        if query:
            params["q"] = query
        if citation:
            params["citation"] = citation
        if docket_number:
            params["docket_number"] = docket_number
        if case_name:
            params["case_name"] = case_name
        if jurisdiction:
            params["court__jurisdiction"] = jurisdiction
        if court:
            params["court"] = court
        if judge:
            params["judge"] = judge
        if status:
            params["status"] = status
            
        # Add any additional parameters
        params.update(kwargs)
        
        return self._make_request(endpoint, params=params)
    
    def get_cases_by_citation(self, citation: str) -> List[Case]:
        """
        Get cases by citation
        
        Args:
            citation: Case citation (e.g., "410 U.S. 113")
            
        Returns:
            List of Case objects matching the citation
        """
        response = self.search_cases(citation=citation)
        cases = []
        
        # Handle different response formats
        if isinstance(response, dict) and "results" in response:
            results = response.get("results", [])
        elif isinstance(response, list):
            results = response
        else:
            results = []
            logger.warning(f"Unexpected response format from citation search: {type(response)}")
        
        for result in results:
            if isinstance(result, dict):
                cases.append(Case.from_api_response(result))
            
        return cases
    
    def get_cluster(self, cluster_id: str) -> Dict:
        """
        Get a specific cluster (case) by ID
        
        Args:
            cluster_id: Court Listener cluster ID
            
        Returns:
            Cluster data as dictionary
            
        Raises:
            CourtListenerResourceNotFoundError: If cluster not found
        """
        endpoint = f"clusters/{cluster_id}/"
        return self._make_request(endpoint)
    
    def get_case(self, case_id: str) -> Case:
        """
        Get a specific case by ID (alias for get_cluster for backward compatibility)
        
        Args:
            case_id: Court Listener cluster ID
            
        Returns:
            Case object
            
        Raises:
            CourtListenerResourceNotFoundError: If case not found
        """
        response = self.get_cluster(case_id)
        return Case.from_api_response(response)
    
    def get_opinion(self, opinion_id: str, full_case: bool = True) -> Union[Dict, Opinion]:
        """
        Get a specific opinion by ID
        
        Args:
            opinion_id: Court Listener opinion ID
            full_case: Whether to include full opinion text (default: True)
            
        Returns:
            Opinion data as dictionary or Opinion object
            
        Raises:
            CourtListenerResourceNotFoundError: If opinion not found
        """
        endpoint = f"opinions/{opinion_id}/"
        params = {"full_case": "true" if full_case else "false"}
        response = self._make_request(endpoint, params=params)
        return Opinion.from_api_response(response)
    
    def get_opinion_raw(self, opinion_id: str, full_case: bool = True) -> Dict:
        """
        Get a specific opinion by ID returning the raw data
        
        Args:
            opinion_id: Court Listener opinion ID
            full_case: Whether to include full opinion text (default: True)
            
        Returns:
            Opinion data as dictionary
            
        Raises:
            CourtListenerResourceNotFoundError: If opinion not found
        """
        endpoint = f"opinions/{opinion_id}/"
        params = {"full_case": "true" if full_case else "false"}
        return self._make_request(endpoint, params=params)
    
    def get_opinions_by_cluster(self, cluster_id: str, full_case: bool = True) -> List[Dict]:
        """
        Get all opinions for a cluster in raw dict format
        
        Args:
            cluster_id: Court Listener cluster ID
            full_case: Whether to include full opinion text (default: True)
            
        Returns:
            List of opinion data dictionaries with full text if requested
        """
        endpoint = "opinions/"
        params = {
            "cluster": cluster_id,
            "full_case": "true" if full_case else "false"
        }
        response = self._make_request(endpoint, params=params)
        
        return response.get("results", [])
    
    def get_opinions_by_case(self, case_id: str, full_case: bool = True) -> List[Opinion]:
        """
        Get all opinions for a case (backward compatibility method)
        
        Args:
            case_id: Court Listener case/cluster ID
            full_case: Whether to include full opinion text (default: True)
            
        Returns:
            List of Opinion objects with full text if requested
        """
        raw_opinions = self.get_opinions_by_cluster(case_id, full_case=full_case)
        
        opinions = []
        for result in raw_opinions:
            opinions.append(Opinion.from_api_response(result))
            
        return opinions
    
    def get_courts(self, jurisdiction: Optional[str] = None) -> List[Court]:
        """
        Get list of courts, optionally filtered by jurisdiction
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tex" for Texas)
            
        Returns:
            List of Court objects
        """
        endpoint = "courts/"
        params = {}
        if jurisdiction:
            params["jurisdiction"] = jurisdiction
            
        response = self._make_request(endpoint, params=params)
        
        courts = []
        for result in response.get("results", []):
            courts.append(Court(
                id=result.get("id", ""),
                name=result.get("full_name", "") or result.get("short_name", ""),
                jurisdiction=result.get("jurisdiction", ""),
                citation_string=result.get("citation_string", None),
                url=result.get("resource_uri", None)
            ))
            
        return courts
        
    def get_court_raw(self, court_id: str) -> Dict:
        """
        Get a specific court by ID returning the raw data
        
        Args:
            court_id: Court Listener court ID
            
        Returns:
            Court data as dictionary
            
        Raises:
            CourtListenerResourceNotFoundError: If court not found
        """
        endpoint = f"courts/{court_id}/"
        return self._make_request(endpoint)
    
    def search_by_jurisdiction(self, jurisdiction: str, query: Optional[str] = None, 
                              page: int = 1, page_size: int = 20, **kwargs) -> Dict:
        """
        Search for cases within a specific jurisdiction
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tex" for Texas)
            query: Search query
            page: Page number
            page_size: Results per page
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        return self.search_cases(
            query=query,
            jurisdiction=jurisdiction,
            page=page,
            page_size=page_size,
            **kwargs
        )
    
    def get_texas_cases(self, query: Optional[str] = None, **kwargs) -> Dict:
        """
        Convenience method to search for Texas cases
        
        Args:
            query: Search query
            **kwargs: Additional search parameters
            
        Returns:
            Search results for Texas cases
        """
        return self.search_by_jurisdiction("tex", query, **kwargs)
        
    def search_opinions(self, q: str,
                          type: str = "o",
                          ordering: str = "decision_date desc",
                          page: int = 1,
                          page_size: int = 20,
                          **kwargs) -> Dict:
        """
        Search Court Listener using the v3 /search/ endpoint.
 
        This endpoint allows complex queries combining keywords and fielded search.
        See Memory [228440f3-b0f5-4871-a95b-cc123b45e332] for query examples.
 
        Args:
            q: The search query string. MUST include any fielded search terms
               like `court_id:(...)` directly within this string.
            type: The type of document to search for (default: "o" for opinions).
            ordering: How to order the results (default: "decision_date desc").
            page: Page number for pagination
            page_size: Number of results per page
            **kwargs: Additional search parameters supported by the /search/ endpoint.
 
        Returns:
            Search results dictionary from the API.
        """
        endpoint = "search/" # Use the v3 search endpoint
        params = {
            "format": "json",
            "q": q,              # Pass the combined query string
            "type": type,        # Specify the document type
            "order_by": ordering, # Use 'order_by' parameter for search endpoint
            "page": page,
            "page_size": page_size
        }
        
        # Add any additional parameters
        params.update(kwargs)
        
        logger.info(f"Sending request to {endpoint} with params: {params}")
        return self._make_request(endpoint, params=params)
        
    def get_docket(self, docket_id: str) -> Dict:
        """
        Get a specific docket by ID
        
        Args:
            docket_id: Court Listener docket ID
            
        Returns:
            Docket data as dictionary
            
        Raises:
            CourtListenerResourceNotFoundError: If docket not found
        """
        endpoint = f"dockets/{docket_id}/"
        return self._make_request(endpoint)
        
    def get_complete_case(self, cluster_id: str, full_case: bool = True) -> Dict:
        """
        Get a complete case including cluster, opinions, and docket information
        
        Args:
            cluster_id: Court Listener cluster ID
            full_case: Whether to include full opinion text (default: True)
            
        Returns:
            Dictionary with complete case data
            
        Raises:
            CourtListenerResourceNotFoundError: If resources not found
        """
        # Get the cluster data
        cluster = self.get_cluster(cluster_id)
        
        # Get related opinions
        opinion_urls = cluster.get("sub_opinions", [])
        opinions = []
        for url in opinion_urls:
            opinion_id = url.split("/")[-2]
            opinion = self.get_opinion_raw(opinion_id, full_case=full_case)
            opinions.append(opinion)
        
        # Get the docket
        docket_url = cluster.get("docket")
        docket = None
        if docket_url:
            docket_id = docket_url.split("/")[-2]
            docket = self.get_docket(docket_id)
        
        # Get court data if available
        court = None
        if docket and "court" in docket and docket["court"]:
            court_url = docket["court"]
            if isinstance(court_url, str):
                court_id = court_url.split("/")[-2]
                court = self.get_court_raw(court_id)
        
        return {
            "cluster": cluster,
            "opinions": opinions,
            "docket": docket,
            "court": court
        }
