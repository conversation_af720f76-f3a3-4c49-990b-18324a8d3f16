"""
Texas Case Law Specialized Module
Provides optimized access to Texas case law through Court Listener API
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

from .client import CourtListenerClient
from .models import Case, Opinion, Court
from .exceptions import CourtListenerAPIError
from .utils import standardize_jurisdiction, format_date

# Configure logging
logger = logging.getLogger(__name__)


class TexasCaseClient:
    """
    Specialized client for Texas case law
    Provides optimized access to Texas-specific case law
    """
    
    # We'll use a court name filter approach instead of jurisdiction code
    # since the API doesn't have a specific Texas jurisdiction code
    TEXAS_STATE_CODE = "TX"
    TEXAS_SEARCH_TERMS = "texas"
    TEXAS_COURTS = {
        "Supreme Court of Texas": "tex-sc",
        "Texas Court of Criminal Appeals": "tex-cca",
        "Texas Court of Appeals": "tex-ca",  # Contains multiple district courts
        "Texas District Courts": "tex-dist"
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Texas case client"""
        self.client = CourtListenerClient(api_key)
        self._courts = None  # Cache for Texas courts
    
    def search(self, query: str, page: int = 1, page_size: int = 20, 
               court_type: Optional[str] = None, year_min: Optional[int] = None, 
               year_max: Optional[int] = None, **kwargs) -> Dict:
        """
        Search for Texas cases
        
        Args:
            query: Search query
            page: Page number
            page_size: Results per page
            court_type: Type of court (supreme, appeals, district)
            year_min: Minimum year for date range
            year_max: Maximum year for date range
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        # Based on the Court Listener documentation, we'll use a text search approach
        # with state filtering to find Texas cases
        params = {
            "page": page,
            "page_size": page_size
        }
        
        # Build the query with Texas filter
        # The "texas" term will find cases with Texas in the text
        # which includes cases heard in Texas courts
        texas_query = f"{query} texas"
        
        # Add date range parameters if provided
        if year_min:
            params["filed_after"] = f"{year_min}-01-01"
        if year_max:
            params["filed_before"] = f"{year_max}-12-31"
        
        # Add court type specificity if provided
        if court_type:
            if court_type.lower() == "supreme":
                texas_query += " supreme court of texas"
            elif court_type.lower() == "appeals":
                texas_query += " court of appeals"
            elif court_type.lower() == "criminal":
                texas_query += " court of criminal appeals"
            elif court_type.lower() == "district":
                texas_query += " district court"
        
        # Add any additional parameters
        params.update(kwargs)
        
        # Use the text-based query approach to find Texas cases
        response = self.client.search_cases(query=texas_query, **params)
        
        # Log the search information
        logger.info(f"Searched for Texas cases with query: '{texas_query}'")
        
        return response
    
    def get_personal_injury_cases(self, subtype: Optional[str] = None, 
                                 page: int = 1, page_size: int = 20) -> Dict:
        """
        Get Texas personal injury cases
        
        Args:
            subtype: Injury subtype (slip-and-fall, auto-accident, etc.)
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results for personal injury cases
        """
        query = "personal injury"
        
        if subtype:
            if subtype.lower() == "slip-and-fall" or subtype.lower() == "premises":
                query += " premises liability slip and fall"
            elif subtype.lower() == "auto" or subtype.lower() == "car":
                query += " automobile accident car crash"
            elif subtype.lower() == "medical":
                query += " medical malpractice"
            elif subtype.lower() == "product":
                query += " product liability defective"
            else:
                query += f" {subtype}"
        
        return self.search(query, page=page, page_size=page_size)
    
    def get_premises_liability_cases(self, page: int = 1, page_size: int = 20) -> Dict:
        """
        Get Texas premises liability cases
        
        Args:
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results for premises liability cases
        """
        return self.search("premises liability", page=page, page_size=page_size)
    
    def get_case_by_id(self, case_id: str) -> Case:
        """
        Get a case by ID
        
        Args:
            case_id: Case ID
            
        Returns:
            Case object
        """
        return self.client.get_case(case_id)
    
    def get_texas_statutes(self, statute: str, page: int = 1, page_size: int = 20) -> Dict:
        """
        Search for Texas cases referencing specific statutes
        
        Args:
            statute: Statute reference (e.g., "CPRC 101.021" for Texas Civil Practice and Remedies Code)
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results mentioning the statute
        """
        query = f"\"{statute}\""
        return self.search(query, page=page, page_size=page_size)
    
    def get_texas_courts(self, refresh: bool = False) -> List[Court]:
        """
        Get list of Texas courts
        
        Args:
            refresh: Whether to refresh the cached courts list
            
        Returns:
            List of Texas Court objects
        """
        if self._courts is None or refresh:
            # Get all courts and filter for Texas
            # Note: The Court Listener API may not have explicit Texas courts 
            # in its court list, but we can still search for Texas cases.
            # This is more of a helper method to identify any Texas-related courts
            # that might be in the system
            all_courts = self.client.get_courts()
            self._courts = []
            
            # Look for courts with Texas in the name
            for court in all_courts:
                if court.name and any(term in court.name.lower() 
                                    for term in ["texas", "tex", "tx"]):
                    self._courts.append(court)
                    
            # If no Texas courts found explicitly, create a synthetic list
            # based on what we know about Texas court structure
            if not self._courts:
                self._courts = [
                    Court(id="tx-sc", name="Supreme Court of Texas", 
                         jurisdiction="TX", citation_string=None, url=None),
                    Court(id="tx-cca", name="Texas Court of Criminal Appeals", 
                         jurisdiction="TX", citation_string=None, url=None),
                    Court(id="tx-ca", name="Texas Courts of Appeals", 
                         jurisdiction="TX", citation_string=None, url=None)
                ]
                
        return self._courts
    
    def get_texas_supreme_court_cases(self, query: str, page: int = 1, page_size: int = 20) -> Dict:
        """
        Get Texas Supreme Court cases
        
        Args:
            query: Search query
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results from the Texas Supreme Court
        """
        court_ids = self._get_court_ids_by_type("supreme")
        return self.search(query, page=page, page_size=page_size, court=court_ids)
    
    def _get_court_ids_by_type(self, court_type: str) -> Optional[str]:
        """
        Get court IDs by type
        
        Args:
            court_type: Type of court (supreme, appeals, district)
            
        Returns:
            Court ID or comma-separated IDs
        """
        if court_type.lower() == "supreme":
            return self.TEXAS_COURTS["Supreme Court of Texas"]
        elif court_type.lower() == "appeals":
            return self.TEXAS_COURTS["Texas Court of Appeals"]
        elif court_type.lower() == "criminal":
            return self.TEXAS_COURTS["Texas Court of Criminal Appeals"]
        elif court_type.lower() == "district":
            return self.TEXAS_COURTS["Texas District Courts"]
        
        # If no match, return None
        return None
        
    def save_search_results(self, results: Union[Dict, List], filename: str) -> str:
        """
        Save search results to a file
        
        Args:
            results: Search results from the API (can be dict or list)
            filename: Filename to save to
            
        Returns:
            Path to the saved file
        """
        # Create the output directory if it doesn't exist
        output_dir = os.path.join("processed_documents", "texas_cases")
        os.makedirs(output_dir, exist_ok=True)
        
        # Add timestamp to filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if not filename.endswith(".json"):
            filename = f"{filename}.json"
        
        full_path = os.path.join(output_dir, f"{filename.replace('.json', '')}_{timestamp}.json")
        
        # Handle different input formats and normalize
        if isinstance(results, list):
            results_list = results
            count = len(results)
            page = 1
            page_count = 1
        elif isinstance(results, dict) and "results" in results:
            results_list = results.get("results", [])
            count = results.get("count", len(results_list))
            page = results.get("page", 1)
            page_count = results.get("page_count", 1)
        else:
            # Handle unexpected format
            logger.warning(f"Unexpected results format: {type(results)}")
            results_list = []
            count = 0
            page = 1
            page_count = 1
        
        # Format the results
        formatted_results = {
            "meta": {
                "timestamp": datetime.now().isoformat(),
                "total_results": count,
                "page": page,
                "page_count": page_count
            },
            "cases": []
        }
        
        # Format each case
        for result in results_list:
            if not isinstance(result, dict):
                logger.warning(f"Skipping non-dict result: {type(result)}")
                continue
                
            # Handle court safely
            court_info = result.get("court", {})
            court_name = "Unknown"
            if isinstance(court_info, dict):
                court_name = court_info.get("full_name", "Unknown")
            
            # Handle citations safely
            citations = result.get("citations", [])
            if not isinstance(citations, list):
                citations = []
            
            case = {
                "id": result.get("id", ""),
                "name": result.get("case_name", "Unknown"),
                "court": court_name,
                "date_filed": result.get("date_filed", "Unknown"),
                "docket_number": result.get("docket_number", "Unknown"),
                "status": result.get("precedential_status", "Unknown"),
                "citations": citations,
                "absolute_url": result.get("absolute_url", "")
            }
            
            formatted_results["cases"].append(case)
        
        # Write to file
        with open(full_path, "w") as f:
            json.dump(formatted_results, f, indent=2)
            
        logger.info(f"Saved {len(formatted_results['cases'])} Texas cases to {full_path}")
        return full_path
