#!/usr/bin/env python3
"""
Integrated Citation Pipeline

Combines citation extraction and enhanced validation into a single workflow.
This pipeline:
1. Extracts citations from documents using the hybrid extractor
2. Classifies citations by type using our new classifier
3. Validates citations with type-specific validation strategies
4. Provides detailed reports on citation quality
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dotenv import load_dotenv

# Import our components
from citation_extractor import HybridCitationExtractor
from citation_classifier import CitationClassifier
from enhanced_citation_validator import EnhancedCitationValidator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("citation_pipeline.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class IntegratedCitationPipeline:
    """
    End-to-end pipeline for citation extraction, classification and validation.
    
    This pipeline connects the citation extraction process with the validation system,
    providing a comprehensive solution for legal document analysis.
    """
    
    def __init__(self):
        """Initialize the integrated citation pipeline components"""
        self.extractor = HybridCitationExtractor()
        self.classifier = CitationClassifier()
        self.validator = EnhancedCitationValidator()
        self.processing_logs = []
    
    def process_document(self, 
                         content: str, 
                         doc_type: str, 
                         document_id: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None,
                         use_llm: bool = True,
                         min_confidence: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a document through the complete citation pipeline.
        
        Args:
            content: Document text content
            doc_type: Document type (e.g., "law", "precedent_case", "regulation")
            document_id: Optional document identifier
            metadata: Optional document metadata
            use_llm: Whether to use LLM for extraction (default True)
            min_confidence: Minimum confidence level for citations
            
        Returns:
            Dictionary with extraction and validation results
        """
        start_time = datetime.now()
        
        # Step 1: Extract citations using existing hybrid extractor
        logger.info(f"Extracting citations from document {document_id or 'unknown'}")
        extracted_citations = self.extractor.extract_citations(
            content, 
            doc_type, 
            doc_metadata=metadata,
            use_llm=use_llm,
            min_confidence=min_confidence
        )
        
        logger.info(f"Extracted {len(extracted_citations)} citations")
        
        # Step 2: Enrich citations with classification data
        enriched_citations = []
        
        for citation in extracted_citations:
            citation_text = citation['text']
            context = citation.get('context', '')
            
            # Classify the citation
            classification_details = self.classifier.get_classification_details(citation_text, context)
            
            # Add classification info to the citation
            enriched_citation = {
                **citation,
                "classification": classification_details['classification'],
                "components": classification_details['components']
            }
            enriched_citations.append(enriched_citation)
        
        logger.info(f"Classified {len(enriched_citations)} citations")
        
        # Step 3: Validate the citations
        validated_citations = []
        citation_types = {}
        valid_count = 0
        
        for citation in enriched_citations:
            citation_text = citation['text']
            citation_type = citation['classification']['type']
            
            # Count citation types
            citation_types[citation_type] = citation_types.get(citation_type, 0) + 1
            
            # Context is already extracted by the extractor
            context = citation.get('context', '')
            
            # Validate the citation
            validation_result = self.validator.validate_citation(citation_text, document_id, context)
            
            # Combine extraction and validation data
            validated_citation = {
                **citation,
                **validation_result
            }
            
            if validation_result['is_valid']:
                valid_count += 1
                
            validated_citations.append(validated_citation)
        
        # Calculate statistics
        total_citations = len(validated_citations)
        valid_rate = valid_count / total_citations if total_citations > 0 else 0
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Build result
        result = {
            "document_id": document_id,
            "document_type": doc_type,
            "extracted_citations": len(extracted_citations),
            "validated_citations": validated_citations,
            "valid_citations": valid_count,
            "valid_rate": valid_rate,
            "citation_types": citation_types,
            "processing_time_seconds": processing_time,
            "timestamp": datetime.now().isoformat()
        }
        
        # Log the processing
        self.processing_logs.append({
            "document_id": document_id,
            "document_type": doc_type,
            "total_citations": total_citations,
            "valid_citations": valid_count,
            "valid_rate": valid_rate,
            "citation_types": citation_types,
            "processing_time_seconds": processing_time,
            "timestamp": datetime.now().isoformat()
        })
        
        logger.info(f"Processed document with {total_citations} citations, {valid_count} valid ({valid_rate:.2%})")
        return result
    
    def batch_process(self, 
                     documents: List[Dict[str, Any]],
                     use_llm: bool = True,
                     min_confidence: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a batch of documents through the citation pipeline.
        
        Args:
            documents: List of document dictionaries with keys:
                - content: Document text
                - doc_type: Document type
                - document_id: Document identifier (optional)
                - metadata: Document metadata (optional)
            use_llm: Whether to use LLM for extraction
            min_confidence: Minimum confidence threshold
            
        Returns:
            Dictionary with batch processing results
        """
        start_time = datetime.now()
        
        batch_results = []
        total_citations = 0
        valid_citations = 0
        citation_types = {}
        
        for doc in documents:
            # Process each document
            result = self.process_document(
                content=doc['content'],
                doc_type=doc['doc_type'],
                document_id=doc.get('document_id'),
                metadata=doc.get('metadata'),
                use_llm=use_llm,
                min_confidence=min_confidence
            )
            
            batch_results.append(result)
            
            # Aggregate statistics
            total_citations += len(result['validated_citations'])
            valid_citations += result['valid_citations']
            
            # Aggregate citation types
            for ctype, count in result['citation_types'].items():
                citation_types[ctype] = citation_types.get(ctype, 0) + count
        
        # Calculate batch statistics
        valid_rate = valid_citations / total_citations if total_citations > 0 else 0
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Build batch result
        batch_result = {
            "batch_id": f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "document_count": len(documents),
            "total_citations": total_citations,
            "valid_citations": valid_citations,
            "valid_rate": valid_rate,
            "citation_types": citation_types,
            "document_results": batch_results,
            "processing_time_seconds": processing_time,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Batch processed: {len(documents)} documents, {total_citations} citations, {valid_rate:.2%} valid")
        return batch_result
    
    def save_processing_log(self, output_path="citation_processing_logs.json"):
        """Save the processing logs to a file"""
        with open(output_path, "w") as f:
            json.dump(self.processing_logs, f, indent=2)
    
    def close(self):
        """Close any open resources"""
        if hasattr(self, 'validator') and self.validator:
            self.validator.close()


# Helper functions for integration with batch processing
def integrate_with_batch_processor(batch_processor, pipeline):
    """
    Extend the batch processor with citation validation capabilities.
    This monkey-patches the batch processor to use our integrated pipeline.
    
    Args:
        batch_processor: The BatchProcessor instance
        pipeline: The IntegratedCitationPipeline instance
    """
    original_process_pdf = batch_processor._process_pdf
    
    def enhanced_process_pdf(self, pdf_path, batch_id, jurisdiction=None):
        """Enhanced PDF processing with citation validation"""
        # First perform the original processing
        result = original_process_pdf(pdf_path, batch_id, jurisdiction)
        
        if result.get('success'):
            doc_id = result.get('doc_id')
            try:
                # Get the processed content
                processed_path = os.path.join(self.success_dir, doc_id)
                with open(os.path.join(processed_path, 'extracted_text.txt'), 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Get document metadata
                metadata = result.get('metadata', {})
                doc_type = metadata.get('doc_type', 'unknown')
                
                # Log the citation validation step
                self.auditor.log_step(doc_id, "citation_validation", {
                    "status": "started",
                })
                
                # Process through our pipeline
                validation_result = pipeline.process_document(
                    content=content,
                    doc_type=doc_type,
                    document_id=doc_id,
                    metadata=metadata
                )
                
                # Save validation results
                validation_path = os.path.join(processed_path, 'citation_validation.json')
                with open(validation_path, 'w', encoding='utf-8') as f:
                    json.dump(validation_result, f, indent=2)
                
                # Update document metadata with citation stats
                metadata.update({
                    "total_citations": len(validation_result['validated_citations']),
                    "valid_citations": validation_result['valid_citations'],
                    "valid_rate": validation_result['valid_rate'],
                    "citation_types": validation_result['citation_types']
                })
                
                # Log the completion of citation validation
                self.auditor.log_step(doc_id, "citation_validation", {
                    "status": "completed",
                    "total_citations": len(validation_result['validated_citations']),
                    "valid_citations": validation_result['valid_citations'],
                    "valid_rate": validation_result['valid_rate']
                })
                
                # Update the document metadata
                self.auditor.complete_document(doc_id, success=True, metadata=metadata)
                
                # Update the result with citation validation info
                result['metadata'] = metadata
                result['citation_validation'] = {
                    "total_citations": len(validation_result['validated_citations']),
                    "valid_citations": validation_result['valid_citations'],
                    "valid_rate": validation_result['valid_rate']
                }
                
            except Exception as e:
                error_msg = f"Error in citation validation: {str(e)}"
                logger.error(error_msg)
                self.auditor.log_error(doc_id, error_msg, {"stage": "citation_validation"})
        
        return result
    
    # Replace the original method with our enhanced one
    batch_processor._process_pdf = enhanced_process_pdf.__get__(batch_processor, type(batch_processor))
    
    return batch_processor


# Example usage for testing
def test_pipeline():
    """Test the integrated citation pipeline with a sample document"""
    sample_text = """
    In Smith v. Jones, 123 F.3d 456 (5th Cir. 2020), the court held that pursuant to Section 123.45 
    of the Texas Civil Code, damages for personal injury claims are not capped in cases involving 
    gross negligence. This ruling overturned the previous precedent established in Johnson v. Medical Center,
    which had limited such damages under Chapter 41 of the Texas Civil Practice and Remedies Code.
    
    According to the Texas Supreme Court in Wilson v. Enterprise, 789 S.W.3d 123 (Tex. 2018), 
    the doctrine of res ipsa loquitur can apply in cases where the defendant had exclusive control 
    of the instrumentality causing the injury. The court cited Article XVI, Section 26 of the Texas 
    Constitution which provides certain protections in personal injury cases.
    """
    
    # Initialize pipeline
    pipeline = IntegratedCitationPipeline()
    
    try:
        # Process the sample document
        result = pipeline.process_document(
            content=sample_text,
            doc_type="precedent_case",
            document_id="sample_doc_001"
        )
        
        # Display results
        print("\n===== INTEGRATED CITATION PIPELINE TEST =====")
        print(f"Total citations: {len(result['validated_citations'])}")
        print(f"Valid citations: {result['valid_citations']}")
        print(f"Valid rate: {result['valid_rate']:.2%}")
        
        print("\nCitation Types:")
        for citation_type, count in result['citation_types'].items():
            print(f"  {citation_type}: {count}")
        
        print("\nValidated Citations:")
        for citation in result['validated_citations']:
            print(f"- {citation['text']}")
            print(f"  Type: {citation['classification']['type']} (Confidence: {citation['classification']['confidence']:.2f})")
            print(f"  Valid: {citation['is_valid']} (Method: {citation['validation_method']})")
            if citation.get('components'):
                print(f"  Components: {citation['components']}")
            if citation.get('suggestions'):
                print(f"  Suggestions: {citation['suggestions']}")
            print()
        
        return result
        
    finally:
        # Ensure resources are closed
        pipeline.close()


if __name__ == "__main__":
    test_pipeline()
