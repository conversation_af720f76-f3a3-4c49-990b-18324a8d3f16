#!/usr/bin/env python3
"""
Demo <PERSON>t for Enhanced Citation Validation

This script demonstrates how to use the enhanced citation validator to analyze
and validate legal citations based on their contextual types.
"""

import os
import json
import argparse
from datetime import datetime
from dotenv import load_dotenv
from enhanced_citation_validator import EnhancedCitationValidator
from citation_classifier import CitationClassifier, CitationType

# Load environment variables
load_dotenv()

def save_results(results, output_file=None):
    """Save validation results to a JSON file"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"validation_results_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"Results saved to: {output_file}")
    return output_file

def test_classifier(test_citations=None):
    """Test the citation classifier on a set of citations"""
    if not test_citations:
        test_citations = [
            "Section 11.20",
            "Acts 1985, 69th Leg., ch. 959, Sec. 1",
            "Brown v. Board of Education, 347 U.S. 483 (1954)",
            "Penal Code § 22.01",
            "Fed. R. Civ. P. 12(b)(6)",
            "U.S. Const. art. I, § 8",
            "42 C.F.R. § 423.10",
            "res judicata"
        ]
    
    print("CITATION CLASSIFIER DEMO")
    print("=" * 50)
    
    classifier = CitationClassifier()
    
    for citation in test_citations:
        result = classifier.get_classification_details(citation)
        print(f"Citation: {citation}")
        print(f"Type: {result['classification']['type']}")
        print(f"Confidence: {result['classification']['confidence']:.2f}")
        print(f"Components: {result['components']}")
        print("-" * 50)
    
    return {
        "test_citations": test_citations,
        "classification_results": [classifier.get_classification_details(citation) for citation in test_citations]
    }

def validate_individual_citations(validator, test_citations=None):
    """Test the validator on individual citations"""
    if not test_citations:
        test_citations = [
            "Section 11.20",
            "Acts 1985, 69th Leg., ch. 959, Sec. 1",
            "Uniform Declaratory Judgments Act",
            "Article I, Section 8 of the Texas Constitution",
            "res judicata"
        ]
    
    print("\nINDIVIDUAL CITATION VALIDATION")
    print("=" * 50)
    
    validation_results = []
    
    for citation in test_citations:
        result = validator.validate_citation(citation)
        validation_results.append(result)
        
        print(f"Citation: {citation}")
        print(f"Type: {result['classification']['type']}")
        print(f"Valid: {result['is_valid']}")
        print(f"Validation method: {result['validation_method']}")
        
        if result['suggestions']:
            print("Suggestions:")
            for suggestion in result['suggestions']:
                print(f"  - {suggestion}")
                
        print("-" * 50)
    
    return {
        "test_citations": test_citations,
        "validation_results": validation_results
    }

def validate_document(validator, document_id=None):
    """Validate all citations in a specific document"""
    if not document_id:
        # Try to find a document with citations to validate
        with validator.driver.session() as session:
            query = """
            MATCH (d:Document)-[:CITES]->(c:Citation)
            WITH d, count(c) as citation_count
            WHERE citation_count > 5
            RETURN d.document_id AS document_id, d.title AS title, citation_count
            ORDER BY citation_count DESC
            LIMIT 1
            """
            result = session.run(query)
            record = result.single()
            
            if record:
                document_id = record["document_id"]
                print(f"Automatically selected document: {record['title']} " + 
                      f"with {record['citation_count']} citations")
            else:
                print("Could not find a document with citations to validate.")
                return None
    
    print(f"\nDOCUMENT CITATION VALIDATION: {document_id}")
    print("=" * 50)
    
    try:
        result = validator.validate_document_citations(document_id)
        
        print(f"Document: {result['document_title']}")
        print(f"Total citations: {result['total_citations']}")
        print(f"Resolved citations: {result['resolved_citations']}")
        print(f"Resolution rate: {result['resolution_rate']:.2f}")
        
        # Show citation type distribution
        print("\nCitation Types:")
        for citation_type, count in result['citation_types'].items():
            print(f"  {citation_type}: {count}")
        
        # Show sample of validated citations
        print("\nSample of validated citations:")
        sample_size = min(5, len(result['validated_citations']))
        for i, citation in enumerate(result['validated_citations'][:sample_size]):
            print(f"  {i+1}. {citation['citation_text']}")
            print(f"     Type: {citation['classification']['type']}")
            print(f"     Valid: {citation['is_valid']}")
            print(f"     Validation method: {citation['validation_method']}")
        
        return result
    except Exception as e:
        print(f"Error validating document {document_id}: {str(e)}")
        return None

def batch_validation(validator, limit=50):
    """Run batch validation on a set of citations"""
    print(f"\nBATCH CITATION VALIDATION (limit: {limit})")
    print("=" * 50)
    
    try:
        result = validator.batch_validate_citations(limit)
        
        print(f"Processed {result['total_citations']} citations")
        print(f"Valid citations: {result['valid_citations']}")
        print(f"Valid rate: {result['valid_rate']:.2f}")
        
        # Show citation type distribution
        print("\nCitation Types:")
        for citation_type, count in result['citation_types'].items():
            print(f"  {citation_type}: {count}")
        
        # Show validation method distribution
        validation_methods = {}
        for citation in result['validation_results']:
            method = citation['validation_method']
            validation_methods[method] = validation_methods.get(method, 0) + 1
            
        print("\nValidation Methods:")
        for method, count in validation_methods.items():
            print(f"  {method}: {count}")
        
        # Show sample of different citation types
        print("\nSamples by Citation Type:")
        
        # Get one example of each type
        type_examples = {}
        for citation in result['validation_results']:
            citation_type = citation['classification']['type']
            if citation_type not in type_examples:
                type_examples[citation_type] = citation
        
        for citation_type, citation in type_examples.items():
            print(f"  Type: {citation_type}")
            print(f"  Example: {citation['citation_text']}")
            print(f"  Valid: {citation['is_valid']}")
            print(f"  Method: {citation['validation_method']}")
            print()
        
        return result
    except Exception as e:
        print(f"Error in batch validation: {str(e)}")
        return None

def generate_report(validator, document_id=None):
    """Generate a comprehensive validation report"""
    if document_id:
        print(f"\nVALIDATION REPORT FOR DOCUMENT: {document_id}")
    else:
        print("\nDATABASE-WIDE VALIDATION REPORT")
    
    print("=" * 50)
    
    try:
        result = validator.generate_validation_report(document_id)
        
        if document_id:
            print(f"Document: {result.get('document_title', 'Unknown')}")
            print(f"Total citations: {result['total_citations']}")
            print(f"Resolved citations: {result['resolved_citations']}")
            print(f"Resolution rate: {result['resolution_rate']:.2f}")
            
            # Show citation type distribution
            print("\nCitation Types:")
            for citation_type, count in result['citation_types'].items():
                print(f"  {citation_type}: {count}")
        else:
            print("Database-wide validation report")
            print(f"Total citations: {result['validation_summary']['total_citations']}")
            print(f"Resolved citations: {result['validation_summary']['resolved_citations']}")
            print(f"Resolution rate: {result['validation_summary']['resolution_rate']:.2f}")
            
            # Show documents with low resolution rates
            if result.get('low_resolution_documents'):
                print("\nDocuments with Low Resolution Rates:")
                for i, doc in enumerate(result['low_resolution_documents']):
                    print(f"  {i+1}. {doc['title']}")
                    print(f"     Resolution rate: {doc['resolution_rate']:.2f}")
                    print(f"     Citations: {doc['total_cites']} total, {doc['resolved_cites']} resolved")
            
            # Show most cited documents
            if result.get('top_cited_documents'):
                print("\nMost Cited Documents:")
                for i, doc in enumerate(result['top_cited_documents']):
                    print(f"  {i+1}. {doc['title']}")
                    print(f"     Citations: {doc['citation_count']}")
        
        # Show insights
        if result.get('insights'):
            print("\nInsights:")
            for insight in result['insights']:
                print(f"- {insight}")
        
        return result
    except Exception as e:
        print(f"Error generating report: {str(e)}")
        return None

def main():
    """Main function to run the demo"""
    parser = argparse.ArgumentParser(description="Enhanced Citation Validation Demo")
    parser.add_argument("--action", choices=[
        "classify", "validate", "document", "batch", "report", "full"
    ], default="full", help="Action to perform")
    
    parser.add_argument("--citation", help="Citation text to validate")
    parser.add_argument("--document", help="Document ID to validate")
    parser.add_argument("--limit", type=int, default=50, help="Limit for batch processing")
    parser.add_argument("--output", help="Output file for results (JSON)")
    
    args = parser.parse_args()
    
    # Run the selected action or full demo
    if args.action == "classify":
        if args.citation:
            results = test_classifier([args.citation])
        else:
            results = test_classifier()
            
    elif args.action == "validate":
        validator = EnhancedCitationValidator()
        try:
            if args.citation:
                results = validate_individual_citations(validator, [args.citation])
            else:
                results = validate_individual_citations(validator)
        finally:
            validator.close()
            
    elif args.action == "document":
        validator = EnhancedCitationValidator()
        try:
            results = validate_document(validator, args.document)
        finally:
            validator.close()
            
    elif args.action == "batch":
        validator = EnhancedCitationValidator()
        try:
            results = batch_validation(validator, args.limit)
        finally:
            validator.close()
            
    elif args.action == "report":
        validator = EnhancedCitationValidator()
        try:
            results = generate_report(validator, args.document)
        finally:
            validator.close()
            
    elif args.action == "full":
        print("FULL ENHANCED CITATION VALIDATION DEMO")
        print("=" * 50)
        
        # First test the classifier
        classification_results = test_classifier()
        
        # Then create validator and run all demos
        validator = EnhancedCitationValidator()
        try:
            individual_results = validate_individual_citations(validator)
            document_results = validate_document(validator)
            batch_results = batch_validation(validator, args.limit)
            report_results = generate_report(validator)
            
            # Combine all results
            results = {
                "classification_demo": classification_results,
                "individual_validation": individual_results,
                "document_validation": document_results,
                "batch_validation": batch_results,
                "validation_report": report_results,
                "timestamp": datetime.now().isoformat()
            }
        finally:
            validator.close()
    
    # Save results if requested
    if args.output and results:
        save_results(results, args.output)

if __name__ == "__main__":
    main()
