# Texas Laws Personal Injury Project

This project provides tools for processing, extracting, and analyzing legal citations in Texas laws, particularly focusing on personal injury statutes.

## Repository Structure

The repository has been organized into the following directory structure for better maintainability and clarity:

```
texas-laws-personalinjury/
├── src/                           # Source code
│   ├── extractors/                # Citation extraction modules
│   ├── validators/                # Validation logic
│   ├── database/                  # Database interactions
│   ├── processing/                # Processing pipelines
│   └── utils/                     # Common utilities
├── apps/                          # Applications
│   └── document_explorer/         # Web app for exploring documents 
├── tests/                         # Testing code
├── scripts/                       # One-off scripts
├── analysis/                      # Analysis scripts
├── logs/                          # Log directory
├── data/                          # Data files
└── documentation/                 # Documentation files
```

## Getting Started

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. Process documents:
   ```
   python -m src.processing.batch_processor
   ```

4. Load citations into Neo4j:
   ```
   python -m src.database.neo4j_citation_loader
   ```

## Key Components

- **Citation Extraction**: Hybrid approach using regex patterns and LLM assistance
- **Document Processing**: Batch processing with error handling and state tracking
- **Neo4j Integration**: Graph database for citation relationships
- **Validation**: Verification of citation accuracy and completeness

## Documentation

For detailed documentation, see the `documentation/` directory and READMEs within each module directory.
