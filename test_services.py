import os
from supabase import create_client
from google.cloud import storage
from pinecone import Pinecone, ServerlessSpec
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Supabase credentials
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

# Pinecone credentials
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")

# GCS credentials
GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
GCS_SERVICE_ACCOUNT_FILE = os.getenv("GCS_SERVICE_ACCOUNT_FILE")


# Supabase Test
def test_supabase(url, key):
    print("\nTesting Supabase...")
    try:
        supabase = create_client(url, key)
        # Check both tables (documents and chunks)
        response_documents = supabase.table("documents").select("*").limit(1).execute()
        response_chunks = supabase.table("chunks").select("*").limit(1).execute()

        print("Supabase connection successful!")
        print("Documents table:", "No data" if not response_documents.data else response_documents.data)
        print("Chunks table:", "No data" if not response_chunks.data else response_chunks.data)
    except Exception as e:
        print("Supabase connection failed:", e)


# Pinecone Test
def test_pinecone(api_key, environment, index_name):
    print("\nTesting Pinecone...")
    try:
        # Create Pinecone instance
        pc = Pinecone(api_key=api_key)

        # List existing indexes
        index_list = pc.list_indexes()
        print(f"Existing indexes: {index_list.names()}")

        # Check if the specified index exists
        if index_name not in index_list.names():
            print(f"Index '{index_name}' does not exist.")
        else:
            index = pc.Index(index_name)
            stats = index.describe_index_stats()
            print("Pinecone connection successful! Index statistics:")
            print(stats)
    except Exception as e:
        print("Pinecone connection failed:", e)


# GCS Test
def test_gcs(bucket_name, service_account_file):
    print("\nTesting GCS...")
    try:
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_file
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        blobs = list(bucket.list_blobs())
        print(f"GCS connection successful! Found {len(blobs)} files in the bucket.")
        if blobs:
            print("Sample file names:")
            for blob in blobs[:5]:  # List first 5 files
                print(f" - {blob.name}")
    except Exception as e:
        print("GCS connection failed:", e)


# Main Function
if __name__ == "__main__":
    # Ensure all environment variables are loaded
    missing_env_vars = [
        var for var in [
            "SUPABASE_URL", "SUPABASE_KEY", "PINECONE_API_KEY",
            "PINECONE_ENVIRONMENT", "PINECONE_INDEX_NAME",
            "GCS_BUCKET_NAME", "GCS_SERVICE_ACCOUNT_FILE"
        ] if not os.getenv(var)
    ]

    if missing_env_vars:
        print(f"Error: Missing environment variables: {', '.join(missing_env_vars)}")
    else:
        # Run tests
        test_supabase(SUPABASE_URL, SUPABASE_KEY)
        test_pinecone(PINECONE_API_KEY, PINECONE_ENVIRONMENT, PINECONE_INDEX_NAME)
        test_gcs(GCS_BUCKET_NAME, GCS_SERVICE_ACCOUNT_FILE)
