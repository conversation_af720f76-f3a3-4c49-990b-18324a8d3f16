    /**
     * Filter documents based on search input
     */
    filterDocuments() {
        const searchInput = document.getElementById('search-documents');
        if (!searchInput) return;
        
        const searchTerm = searchInput.value.toLowerCase();
        
        if (!searchTerm) {
            // If search term is empty, just render all documents
            this.renderDocumentsTable();
            return;
        }
        
        // Filter documents
        const filteredDocs = this.documents.filter(doc => {
            return (
                (doc.title && doc.title.toLowerCase().includes(searchTerm)) ||
                (doc.jurisdiction && doc.jurisdiction.toLowerCase().includes(searchTerm)) ||
                (doc.type && doc.type.toLowerCase().includes(searchTerm))
            );
        });
        
        // Update documents list with filtered results
        const tableBody = document.getElementById('documents-list');
        if (!tableBody) return;
        
        // Clear table
        tableBody.innerHTML = '';
        
        if (filteredDocs.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="5" class="text-center">No matching documents found</td>`;
            tableBody.appendChild(row);
            return;
        }
        
        // Add filtered documents to table
        filteredDocs.forEach(doc => {
            const row = document.createElement('tr');
            
            // Format date
            const createdDate = new Date(doc.created_at);
            const formattedDate = createdDate.toLocaleDateString();
            
            row.innerHTML = `
                <td>${doc.title}</td>
                <td>${doc.type}</td>
                <td>${doc.jurisdiction}</td>
                <td>${formattedDate}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-document-btn" data-document-id="${doc.id}">View</button>
                    <button class="btn btn-sm btn-outline-primary view-graph-btn" data-document-id="${doc.id}">Graph</button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to buttons
        document.querySelectorAll('.view-document-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const documentId = e.target.getAttribute('data-document-id');
                this.showDocumentDetails(documentId);
            });
        });
        
        document.querySelectorAll('.view-graph-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const documentId = e.target.getAttribute('data-document-id');
                this.navigateToSection('visualization');
                
                // Select document in dropdown
                const visualizationDocument = document.getElementById('visualization-document');
                if (visualizationDocument) {
                    visualizationDocument.value = documentId;
                    // Trigger change event
                    const event = new Event('change');
                    visualizationDocument.dispatchEvent(event);
                }
            });
        });
    }
    
    /**
     * Show document details modal
     */
    showDocumentDetails(documentId) {
        fetch(`/api/document/${documentId}`)
            .then(response => response.json())
            .then(data => {
                this.currentDocument = data.document;
                
                // Populate modal
                const modalTitle = document.getElementById('document-detail-title');
                const modalContent = document.getElementById('document-detail-content');
                
                if (modalTitle) {
                    modalTitle.textContent = data.document.title || 'Document Details';
                }
                
                if (modalContent) {
                    let contentHTML = `
                        <div class="document-metadata mb-4">
                            <h5>Document Information</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Type:</strong> ${data.document.doc_type || 'Unknown'}</p>
                                    <p><strong>Jurisdiction:</strong> ${data.document.jurisdiction || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                    `;
                    
                    // Add type-specific metadata
                    if (data.document.doc_type === 'law') {
                        contentHTML += `
                            <p><strong>Statute Title:</strong> ${data.document.statute_title || 'N/A'}</p>
                            <p><strong>Statute Section:</strong> ${data.document.statute_section || 'N/A'}</p>
                        `;
                    } else if (data.document.doc_type === 'precedent_case') {
                        contentHTML += `
                            <p><strong>Case Number:</strong> ${data.document.case_number || 'N/A'}</p>
                            <p><strong>Court:</strong> ${data.document.court || 'N/A'}</p>
                            <p><strong>Case Date:</strong> ${data.document.case_date || 'N/A'}</p>
                        `;
                    }
                    
                    contentHTML += `
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Add citations
                    contentHTML += `<h5>Outgoing Citations</h5>`;
                    
                    if (data.outgoing_citations && data.outgoing_citations.length > 0) {
                        contentHTML += `<div class="citation-list mb-4">`;
                        data.outgoing_citations.forEach(citation => {
                            contentHTML += `
                                <div class="citation-item">
                                    <div class="d-flex justify-content-between">
                                        <strong>${citation.citation_text}</strong>
                                        <span class="badge bg-secondary">${citation.citation_type || 'Unknown'}</span>
                                    </div>
                            `;
                            
                            if (citation.cited_document_id) {
                                contentHTML += `
                                    <div class="mt-1">
                                        Resolves to: <a href="#" class="document-link" data-document-id="${citation.cited_document_id}">${citation.cited_document_title}</a>
                                    </div>
                                `;
                            }
                            
                            contentHTML += `</div>`;
                        });
                        contentHTML += `</div>`;
                    } else {
                        contentHTML += `<p>No citations found in this document.</p>`;
                    }
                    
                    // Add references
                    contentHTML += `<h5>Cited By</h5>`;
                    
                    if (data.incoming_citations && data.incoming_citations.length > 0) {
                        contentHTML += `<div class="citation-list mb-4">`;
                        data.incoming_citations.forEach(citation => {
                            contentHTML += `
                                <div class="citation-item">
                                    <div>
                                        Cited by: <a href="#" class="document-link" data-document-id="${citation.citing_document_id}">${citation.citing_document_title}</a>
                                    </div>
                                    <div class="citation-context">
                                        "${citation.citation_text}"
                                    </div>
                                </div>
                            `;
                        });
                        contentHTML += `</div>`;
                    } else {
                        contentHTML += `<p>This document is not cited by other documents.</p>`;
                    }
                    
                    modalContent.innerHTML = contentHTML;
                    
                    // Add event listeners to document links
                    document.querySelectorAll('.document-link').forEach(link => {
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            const linkedDocId = e.target.getAttribute('data-document-id');
                            
                            // Hide current modal
                            const currentModal = bootstrap.Modal.getInstance(document.getElementById('document-detail-modal'));
                            currentModal.hide();
                            
                            // Show linked document
                            this.showDocumentDetails(linkedDocId);
                        });
                    });
                }
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('document-detail-modal'));
                modal.show();
            })
            .catch(error => {
                console.error('Error loading document details:', error);
                alert('Error loading document details');
            });
    }
    
    /**
     * Load documents for visualization dropdown
     */
    loadDocumentsForVisualization() {
        fetch('/api/documents')
            .then(response => response.json())
            .then(data => {
                // Populate document dropdown
                const visualizationDocument = document.getElementById('visualization-document');
                if (visualizationDocument) {
                    // Clear existing options except the first one
                    while (visualizationDocument.options.length > 1) {
                        visualizationDocument.remove(1);
                    }
                    
                    // Add document options
                    data.forEach(doc => {
                        const option = document.createElement('option');
                        option.value = doc.id;
                        option.textContent = doc.title;
                        visualizationDocument.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading documents for visualization:', error);
            });
    }
    
    /**
     * Load graph data for visualization
     */
    loadGraphData(documentId, depth = 2) {
        fetch(`/api/graph/${documentId}?depth=${depth}`)
            .then(response => response.json())
            .then(data => {
                this.renderGraph(data);
            })
            .catch(error => {
                console.error('Error loading graph data:', error);
                this.clearGraph();
            });
    }
    
    /**
     * Render the graph visualization
     */
    renderGraph(data) {
        const container = document.getElementById('graph-container');
        if (!container) return;
        
        // Clear previous graph
        container.innerHTML = '';
        
        if (!data.nodes || data.nodes.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No relationship data available for this document.</div>';
            return;
        }
        
        // Set up D3.js force simulation
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        // Create SVG
        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height);
        
        // Define node colors based on type
        const nodeColors = {
            'Document': '#4e79a7',
            'Law': '#4e79a7',
            'Case': '#f28e2c',
            'Citation': '#59a14f',
            'Regulation': '#e15759',
            'Ruling': '#76b7b2'
        };
        
        // Define link colors based on type
        const linkColors = {
            'CITES': '#333',
            'RESOLVES_TO': '#ff7f0e'
        };
        
        // Create force simulation
        this.graphSimulation = d3.forceSimulation(data.nodes)
            .force('link', d3.forceLink(data.links).id(d => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(50));
        
        // Add links
        const link = svg.append('g')
            .selectAll('line')
            .data(data.links)
            .enter()
            .append('line')
            .attr('stroke', d => linkColors[d.type] || '#999')
            .attr('stroke-width', 1.5)
            .attr('stroke-opacity', 0.6);
        
        // Add nodes
        const node = svg.append('g')
            .selectAll('g')
            .data(data.nodes)
            .enter()
            .append('g')
            .call(d3.drag()
                .on('start', this.dragstarted.bind(this))
                .on('drag', this.dragged.bind(this))
                .on('end', this.dragended.bind(this)));
        
        // Add circles for nodes
        node.append('circle')
            .attr('r', d => d.label === 'Citation' ? 5 : 10)
            .attr('fill', d => nodeColors[d.type] || '#999');
        
        // Add text labels
        node.append('text')
            .attr('x', 12)
            .attr('y', 3)
            .style('font-size', '10px')
            .text(d => d.title ? (d.title.length > 30 ? d.title.substring(0, 30) + '...' : d.title) : '');
        
        // Add tooltips
        node.append('title')
            .text(d => d.title || d.label);
        
        // Update positions on simulation tick
        this.graphSimulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);
            
            node
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });
    }
    
    /**
     * Handle drag start on graph
     */
    dragstarted(event, d) {
        if (!event.active) this.graphSimulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }
    
    /**
     * Handle drag on graph
     */
    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }
    
    /**
     * Handle drag end on graph
     */
    dragended(event, d) {
        if (!event.active) this.graphSimulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }
    
    /**
     * Clear the graph visualization
     */
    clearGraph() {
        const container = document.getElementById('graph-container');
        if (container) {
            container.innerHTML = '';
        }
        
        if (this.graphSimulation) {
            this.graphSimulation.stop();
            this.graphSimulation = null;
        }
    }
    
    /**
     * Load statistics data
     */
    loadStatistics() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                // Process jurisdiction data
                const jurisdictionCounts = {};
                const typeCounts = {};
                
                data.document_counts.forEach(item => {
                    // By jurisdiction
                    if (!jurisdictionCounts[item.jurisdiction]) {
                        jurisdictionCounts[item.jurisdiction] = 0;
                    }
                    jurisdictionCounts[item.jurisdiction] += item.count;
                    
                    // By type
                    if (!typeCounts[item.doc_type]) {
                        typeCounts[item.doc_type] = 0;
                    }
                    typeCounts[item.doc_type] += item.count;
                });
                
                // Render jurisdiction chart
                const jurisdictionChart = document.getElementById('jurisdiction-chart');
                if (jurisdictionChart) {
                    // Check if chart already exists
                    if (jurisdictionChart._chart) {
                        jurisdictionChart._chart.destroy();
                    }
                    
                    const ctx = jurisdictionChart.getContext('2d');
                    jurisdictionChart._chart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: Object.keys(jurisdictionCounts),
                            datasets: [{
                                label: 'Documents',
                                data: Object.values(jurisdictionCounts),
                                backgroundColor: 'rgba(78, 121, 167, 0.7)',
                                borderColor: 'rgba(78, 121, 167, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
                
                // Render document type chart
                const docTypeChart = document.getElementById('doc-type-chart');
                if (docTypeChart) {
                    // Check if chart already exists
                    if (docTypeChart._chart) {
                        docTypeChart._chart.destroy();
                    }
                    
                    const ctx = docTypeChart.getContext('2d');
                    docTypeChart._chart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: Object.keys(typeCounts),
                            datasets: [{
                                data: Object.values(typeCounts),
                                backgroundColor: [
                                    'rgba(78, 121, 167, 0.7)',
                                    'rgba(242, 142, 44, 0.7)',
                                    'rgba(225, 87, 89, 0.7)',
                                    'rgba(118, 183, 178, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(78, 121, 167, 1)',
                                    'rgba(242, 142, 44, 1)',
                                    'rgba(225, 87, 89, 1)',
                                    'rgba(118, 183, 178, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }
                
                // Render citation statistics
                const citationStats = document.getElementById('citation-stats');
                if (citationStats) {
                    const stats = data.citation_stats;
                    
                    citationStats.innerHTML = `
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-value">${stats.total_citations}</div>
                                    <div class="stat-label">Total Citations</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-value">${stats.unique_citations}</div>
                                    <div class="stat-label">Unique Citations</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-value">${stats.citing_documents}</div>
                                    <div class="stat-label">Citing Documents</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="stat-value">${stats.avg_citations_per_doc.toFixed(2)}</div>
                                    <div class="stat-label">Avg Citations Per Doc</div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
            });
    }
}

// Initialize document explorer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.explorer = new DocumentExplorer();
});
