    /**
     * Load dashboard data
     */
    loadDashboardData() {
        // Load document distribution chart
        this.loadDocumentDistributionChart();
        
        // Load citation stats chart
        this.loadCitationStatsChart();
        
        // Load top cited documents
        this.loadTopCitedDocuments();
    }
    
    /**
     * Load document distribution chart
     */
    loadDocumentDistributionChart() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                // Process data for chart
                const jurisdictionCounts = {};
                const typeCounts = {};
                
                data.document_counts.forEach(item => {
                    // By jurisdiction
                    if (!jurisdictionCounts[item.jurisdiction]) {
                        jurisdictionCounts[item.jurisdiction] = 0;
                    }
                    jurisdictionCounts[item.jurisdiction] += item.count;
                    
                    // By type
                    if (!typeCounts[item.doc_type]) {
                        typeCounts[item.doc_type] = 0;
                    }
                    typeCounts[item.doc_type] += item.count;
                });
                
                // Render chart
                const chartElement = document.getElementById('document-distribution-chart');
                if (chartElement) {
                    // Check if chart already exists
                    if (chartElement._chart) {
                        chartElement._chart.destroy();
                    }
                    
                    const ctx = chartElement.getContext('2d');
                    chartElement._chart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: Object.keys(jurisdictionCounts),
                            datasets: [{
                                label: 'Documents by Jurisdiction',
                                data: Object.values(jurisdictionCounts),
                                backgroundColor: 'rgba(78, 121, 167, 0.7)',
                                borderColor: 'rgba(78, 121, 167, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error loading document distribution chart:', error);
            });
    }
    
    /**
     * Load citation stats chart
     */
    loadCitationStatsChart() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                const citationStats = data.citation_stats;
                
                // Render chart
                const chartElement = document.getElementById('citation-stats-chart');
                if (chartElement) {
                    // Check if chart already exists
                    if (chartElement._chart) {
                        chartElement._chart.destroy();
                    }
                    
                    const ctx = chartElement.getContext('2d');
                    chartElement._chart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Citations', 'Unique Citations', 'Citing Documents'],
                            datasets: [{
                                data: [
                                    citationStats.total_citations,
                                    citationStats.unique_citations,
                                    citationStats.citing_documents
                                ],
                                backgroundColor: [
                                    'rgba(78, 121, 167, 0.7)',
                                    'rgba(242, 142, 44, 0.7)',
                                    'rgba(225, 87, 89, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(78, 121, 167, 1)',
                                    'rgba(242, 142, 44, 1)',
                                    'rgba(225, 87, 89, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.label + ': ' + context.raw;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error loading citation stats chart:', error);
            });
    }
    
    /**
     * Load top cited documents table
     */
    loadTopCitedDocuments() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                const topCited = data.top_cited_documents;
                
                // Render table
                const tableElement = document.getElementById('top-cited-table');
                if (tableElement) {
                    let tableHTML = `
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Document Title</th>
                                    <th>Type</th>
                                    <th>Jurisdiction</th>
                                    <th>Cited By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    if (topCited && topCited.length > 0) {
                        topCited.forEach(doc => {
                            tableHTML += `
                                <tr>
                                    <td>${doc.title}</td>
                                    <td>${doc.doc_type}</td>
                                    <td>${doc.jurisdiction}</td>
                                    <td>${doc.cited_by_count}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary view-document-btn" data-document-id="${doc.document_id}">View</button>
                                        <button class="btn btn-sm btn-outline-primary view-graph-btn" data-document-id="${doc.document_id}">Graph</button>
                                    </td>
                                </tr>
                            `;
                        });
                    } else {
                        tableHTML += `
                            <tr>
                                <td colspan="5" class="text-center">No citation data available</td>
                            </tr>
                        `;
                    }
                    
                    tableHTML += `
                            </tbody>
                        </table>
                    `;
                    
                    tableElement.innerHTML = tableHTML;
                    
                    // Add event listeners to buttons
                    document.querySelectorAll('.view-document-btn').forEach(button => {
                        button.addEventListener('click', (e) => {
                            const documentId = e.target.getAttribute('data-document-id');
                            this.showDocumentDetails(documentId);
                        });
                    });
                    
                    document.querySelectorAll('.view-graph-btn').forEach(button => {
                        button.addEventListener('click', (e) => {
                            const documentId = e.target.getAttribute('data-document-id');
                            this.navigateToSection('visualization');
                            
                            // Select document in dropdown
                            const visualizationDocument = document.getElementById('visualization-document');
                            if (visualizationDocument) {
                                visualizationDocument.value = documentId;
                                // Trigger change event
                                const event = new Event('change');
                                visualizationDocument.dispatchEvent(event);
                            }
                        });
                    });
                }
            })
            .catch(error => {
                console.error('Error loading top cited documents:', error);
            });
    }
    
    /**
     * Load documents for the documents section
     */
    loadDocuments() {
        // Apply any active filters
        const jurisdictionFilter = document.getElementById('jurisdiction-filter');
        const docTypeFilter = document.getElementById('doc-type-filter');
        const searchDocuments = document.getElementById('search-documents');
        
        let url = '/api/documents?';
        
        if (jurisdictionFilter && jurisdictionFilter.value) {
            url += `jurisdiction=${encodeURIComponent(jurisdictionFilter.value)}&`;
        }
        
        if (docTypeFilter && docTypeFilter.value) {
            url += `doc_type=${encodeURIComponent(docTypeFilter.value)}&`;
        }
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                this.documents = data;
                this.renderDocumentsTable();
                
                // Apply search filter if active
                if (searchDocuments && searchDocuments.value) {
                    this.filterDocuments();
                }
            })
            .catch(error => {
                console.error('Error loading documents:', error);
            });
    }
    
    /**
     * Render the documents table
     */
    renderDocumentsTable() {
        const tableBody = document.getElementById('documents-list');
        if (!tableBody) return;
        
        // Clear table
        tableBody.innerHTML = '';
        
        if (this.documents.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="5" class="text-center">No documents found</td>`;
            tableBody.appendChild(row);
            return;
        }
        
        // Add documents to table
        this.documents.forEach(doc => {
            const row = document.createElement('tr');
            
            // Format date
            const createdDate = new Date(doc.created_at);
            const formattedDate = createdDate.toLocaleDateString();
            
            row.innerHTML = `
                <td>${doc.title}</td>
                <td>${doc.type}</td>
                <td>${doc.jurisdiction}</td>
                <td>${formattedDate}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-document-btn" data-document-id="${doc.id}">View</button>
                    <button class="btn btn-sm btn-outline-primary view-graph-btn" data-document-id="${doc.id}">Graph</button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // Add event listeners to buttons
        document.querySelectorAll('.view-document-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const documentId = e.target.getAttribute('data-document-id');
                this.showDocumentDetails(documentId);
            });
        });
        
        document.querySelectorAll('.view-graph-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const documentId = e.target.getAttribute('data-document-id');
                this.navigateToSection('visualization');
                
                // Select document in dropdown
                const visualizationDocument = document.getElementById('visualization-document');
                if (visualizationDocument) {
                    visualizationDocument.value = documentId;
                    // Trigger change event
                    const event = new Event('change');
                    visualizationDocument.dispatchEvent(event);
                }
            });
        });
    }
