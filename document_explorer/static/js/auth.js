/**
 * Authentication Service for Legal Document Explorer
 * Implements secure JWT token handling with role-based access control
 */

class AuthService {
    constructor() {
        this.currentUser = null;
        this.roles = {
            'partner': { level: 4, label: 'Partner' },
            'attorney': { level: 3, label: 'Attorney' },
            'paralegal': { level: 2, label: 'Paralegal' },
            'staff': { level: 1, label: 'Staff' },
            'client': { level: 0, label: 'Client' }
        };
        this.tokenRefreshInterval = null;
        this.authStateListeners = [];
        
        // Initialize auth state
        this.checkAuthState();
    }
    
    /**
     * Check current authentication state from JWT token
     */
    checkAuthState() {
        try {
            // JWT is stored in HttpOnly cookie so we can't access it directly
            // Instead, we call a verification endpoint that returns user data if authenticated
            fetch('/api/auth/verify', {
                method: 'GET',
                credentials: 'include' // Important: Include cookies in the request
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                this.handleUnauthenticated();
                return null;
            })
            .then(data => {
                if (data) {
                    this.handleAuthenticated(data);
                }
            })
            .catch(error => {
                console.error('Auth verification error:', error);
                this.handleUnauthenticated();
            });
        } catch (error) {
            console.error('Auth check error:', error);
            this.handleUnauthenticated();
        }
    }
    
    /**
     * Handle successful authentication
     */
    handleAuthenticated(userData) {
        this.currentUser = userData;
        
        // Setup token refresh (tokens should be short-lived)
        if (this.tokenRefreshInterval) {
            clearInterval(this.tokenRefreshInterval);
        }
        
        // Refresh token 1 minute before expiry
        const tokenLifetime = (userData.exp - userData.iat) * 1000;
        const refreshTime = Math.max(tokenLifetime - 60000, tokenLifetime / 2);
        
        this.tokenRefreshInterval = setInterval(() => {
            this.refreshToken();
        }, refreshTime);
        
        // Update UI based on role
        this.updateAuthUI();
        
        // Notify listeners
        this.notifyAuthStateListeners();
    }
    
    /**
     * Handle unauthenticated state
     */
    handleUnauthenticated() {
        this.currentUser = null;
        
        if (this.tokenRefreshInterval) {
            clearInterval(this.tokenRefreshInterval);
            this.tokenRefreshInterval = null;
        }
        
        // Update UI for unauthenticated state
        this.updateAuthUI();
        
        // Notify listeners
        this.notifyAuthStateListeners();
        
        // If not on public route, redirect to login
        const publicRoutes = ['/', '/login', '/register', '/privacy-policy', '/terms'];
        const currentPath = window.location.pathname;
        
        if (!publicRoutes.some(route => currentPath === route || currentPath === route + '/')) {
            window.location.href = '/login?redirect=' + encodeURIComponent(currentPath);
        }
    }
    
    /**
     * Refresh the JWT token
     */
    refreshToken() {
        fetch('/api/auth/refresh', {
            method: 'POST',
            credentials: 'include'
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            }
            throw new Error('Token refresh failed');
        })
        .then(data => {
            this.handleAuthenticated(data);
        })
        .catch(error => {
            console.error('Token refresh error:', error);
            this.handleUnauthenticated();
        });
    }
    
    /**
     * Update the UI based on authentication state
     */
    updateAuthUI() {
        const authSection = document.getElementById('user-auth-section');
        if (!authSection) return;
        
        if (this.currentUser) {
            const roleInfo = this.roles[this.currentUser.role] || { level: 0, label: 'User' };
            
            authSection.innerHTML = `
                <div class="d-flex align-items-center">
                    <span>${this.currentUser.email}</span>
                    <span class="role-indicator role-${this.currentUser.role}">${roleInfo.label}</span>
                    <button class="btn btn-sm btn-outline-light ms-2" id="logout-button">Logout</button>
                </div>
            `;
            
            // Add logout handler
            document.getElementById('logout-button').addEventListener('click', () => {
                this.logout();
            });
            
            // Show/hide elements based on role
            this.applyRoleBasedVisibility();
        } else {
            authSection.innerHTML = `
                <a href="/login" class="btn btn-outline-light">Login</a>
                <a href="/register" class="btn btn-light ms-2">Register</a>
            `;
        }
    }
    
    /**
     * Apply role-based visibility to elements
     */
    applyRoleBasedVisibility() {
        if (!this.currentUser) return;
        
        const userRoleLevel = (this.roles[this.currentUser.role] || { level: 0 }).level;
        
        // Staff routes require at least paralegal role (level 2)
        document.querySelectorAll('[data-required-role="staff"]').forEach(el => {
            if (userRoleLevel >= 1) {
                el.classList.remove('d-none');
            } else {
                el.classList.add('d-none');
            }
        });
        
        // Admin routes require partner role (level 4)
        document.querySelectorAll('[data-required-role="admin"]').forEach(el => {
            if (userRoleLevel >= 4) {
                el.classList.remove('d-none');
            } else {
                el.classList.add('d-none');
            }
        });
        
        // Client routes require any authenticated user
        document.querySelectorAll('[data-required-role="client"]').forEach(el => {
            el.classList.remove('d-none');
        });
        
        // Tenant filtering
        if (this.currentUser.tenant_id) {
            // Show tenant indicator
            const tenantIndicator = document.getElementById('tenant-indicator');
            if (tenantIndicator) {
                tenantIndicator.textContent = `Tenant: ${this.currentUser.tenant_id}`;
                tenantIndicator.classList.remove('d-none');
            }
        }
    }
    
    /**
     * Log the user out
     */
    logout() {
        fetch('/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
        })
        .finally(() => {
            // Clear state and redirect to home
            this.handleUnauthenticated();
            window.location.href = '/';
        });
    }
    
    /**
     * Check if user has a specific role
     */
    hasRole(requiredRole) {
        if (!this.currentUser) return false;
        
        const userRoleLevel = (this.roles[this.currentUser.role] || { level: 0 }).level;
        const requiredRoleLevel = (this.roles[requiredRole] || { level: 0 }).level;
        
        return userRoleLevel >= requiredRoleLevel;
    }
    
    /**
     * Get current user's tenant ID
     */
    getTenantId() {
        return this.currentUser ? this.currentUser.tenant_id : null;
    }
    
    /**
     * Add listener for auth state changes
     */
    addAuthStateListener(listener) {
        if (typeof listener === 'function') {
            this.authStateListeners.push(listener);
        }
    }
    
    /**
     * Remove auth state listener
     */
    removeAuthStateListener(listener) {
        const index = this.authStateListeners.indexOf(listener);
        if (index !== -1) {
            this.authStateListeners.splice(index, 1);
        }
    }
    
    /**
     * Notify all auth state listeners
     */
    notifyAuthStateListeners() {
        this.authStateListeners.forEach(listener => {
            try {
                listener(this.currentUser);
            } catch (error) {
                console.error('Error in auth state listener:', error);
            }
        });
    }
}

// Initialize auth service
const authService = new AuthService();

// For demo purposes, add a mock auth check
// In a real app, this would be handled by the server
(function setupMockAuth() {
    // Mock API endpoints with fetch intercepts
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        if (url === '/api/auth/verify') {
            return mockAuthVerify();
        } else if (url === '/api/auth/refresh') {
            return mockAuthRefresh();
        } else if (url === '/api/auth/logout') {
            return mockAuthLogout();
        }
        return originalFetch(url, options);
    };
    
    // Mock verify endpoint
    function mockAuthVerify() {
        return new Promise(resolve => {
            // Check for mock token in localStorage (for demo only)
            // In production, tokens should be in HttpOnly cookies
            const mockToken = localStorage.getItem('mock_auth_token');
            
            if (mockToken) {
                resolve(new Response(JSON.stringify({
                    sub: 'user-123',
                    email: '<EMAIL>',
                    role: 'attorney',
                    tenant_id: 'tenant-456',
                    exp: Math.floor(Date.now() / 1000) + 3600,
                    iat: Math.floor(Date.now() / 1000)
                }), { status: 200, headers: { 'Content-Type': 'application/json' } }));
            } else {
                resolve(new Response(null, { status: 401 }));
            }
        });
    }
    
    // Mock refresh endpoint
    function mockAuthRefresh() {
        return new Promise(resolve => {
            const mockToken = localStorage.getItem('mock_auth_token');
            
            if (mockToken) {
                resolve(new Response(JSON.stringify({
                    sub: 'user-123',
                    email: '<EMAIL>',
                    role: 'attorney',
                    tenant_id: 'tenant-456',
                    exp: Math.floor(Date.now() / 1000) + 3600,
                    iat: Math.floor(Date.now() / 1000)
                }), { status: 200, headers: { 'Content-Type': 'application/json' } }));
            } else {
                resolve(new Response(null, { status: 401 }));
            }
        });
    }
    
    // Mock logout endpoint
    function mockAuthLogout() {
        return new Promise(resolve => {
            localStorage.removeItem('mock_auth_token');
            resolve(new Response(null, { status: 200 }));
        });
    }
    
    // Set the mock token (for demo purposes only)
    // Set to 'attorney' to simulate being logged in
    localStorage.setItem('mock_auth_token', 'mock_token_value');
})();
