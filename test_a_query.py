import os
import json
import logging
import requests
from dotenv import load_dotenv
from pinecone import Pinecone
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
VOYAGE_API_KEY = os.getenv("VOYAGE_API_KEY")

# Initialize Pinecone
pc = Pinecone(api_key=PINECONE_API_KEY)
index = pc.Index(PINECONE_INDEX_NAME)

# Initialize OpenAI client
client = OpenAI(api_key=OPENAI_API_KEY)

# Voyage AI configuration
VOYAGE_EMBEDDING_MODEL = "voyage-3-large"
VOYAGE_API_URL = "https://api.voyageai.com/v1/embeddings"

def search_pinecone(query, namespace, top_k=5):
    """
    Search Pinecone for the most relevant vectors to the query using Voyage AI embeddings.

    :param query: The query text to search for.
    :param namespace: The Pinecone namespace to search within.
    :param top_k: Number of top results to retrieve.
    :return: List of matching results.
    """
    logging.debug("Generating Voyage AI embedding for query: %s", query)
    
    # Use only Voyage AI to maintain embedding space consistency
    try:
        # Generate embedding for the query using Voyage AI
        response = requests.post(
            VOYAGE_API_URL,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {VOYAGE_API_KEY}"
            },
            json={
                "model": VOYAGE_EMBEDDING_MODEL,
                "input": [query],
                "input_type": "query",
                "truncation": True
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            query_embedding = data["data"][0]["embedding"]
            logging.info(f"Successfully generated embedding with Voyage AI ({VOYAGE_EMBEDDING_MODEL})")
        else:
            # No fallback to maintain embedding space consistency
            error_msg = f"Voyage API error: {response.status_code} - {response.text}"
            logging.error(error_msg)
            raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Error with Voyage AI embeddings: {e}"
        logging.error(error_msg)
        raise Exception(error_msg)
    
    logging.debug(f"Generated embedding with dimensions: {len(query_embedding)}")

    # Query Pinecone
    logging.debug("Querying Pinecone with embedding and namespace: %s", namespace)
    results = index.query(
        vector=query_embedding,
        namespace=namespace,
        top_k=top_k,
        include_metadata=True
    )
    logging.debug("Pinecone query results: %s", results)
    return results.matches

def format_context(matches):
    """Format the context from matches with their scores and citation information."""
    context_parts = []
    for i, match in enumerate(matches, 1):
        score = match.score if hasattr(match, 'score') else 'N/A'
        
        # Access content and citation metadata
        text = match.metadata.get('text', match.metadata.get('content', ''))
        citation = match.metadata.get('citation', 'No citation available')
        document_title = match.metadata.get('document_title', 'Untitled')
        page_numbers = match.metadata.get('page_numbers', [])
        
        # Format with proper citation
        page_info = f" (Pages: {', '.join(map(str, page_numbers))})" if page_numbers else ""
        formatted_citation = f"{document_title}{page_info} [{citation}]"
        
        context_parts.append(f"[Passage {i}, Relevance: {score}, Citation: {formatted_citation}]\n{text}\n")
    return "\n".join(context_parts)

def ask_llm(question, context):
    """Ask OpenAI's GPT model using structured prompt with proper citation instructions."""
    prompt = f"""You are a legal assistant. Answer the question using ONLY the provided legal text passages. 
If the answer cannot be fully determined from the passages, say so explicitly.

Legal Context (with citations):
{context}

Question: {question}

Instructions:
1. Base your answer solely on the provided legal passages
2. Quote relevant parts of the text to support your answer
3. ALWAYS include proper citations when referencing legal text
4. Format citations as: [Document Title, Page X-Y]
5. If the passages don't contain enough information, state what's missing

Answer:"""
    
    logging.debug("Sending prompt to GPT model:\n%s", prompt)
    response = client.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": "You are a legal assistant specializing in Texas personal injury law. Only use the provided legal context to answer questions. Always include proper citations in your answers."},
            {"role": "user", "content": prompt}
        ],
        temperature=0  # Lower temperature for more focused answers
    )
    logging.debug("GPT model response: %s", response)
    return response.choices[0].message.content.strip()

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Query Texas personal injury law database')
    parser.add_argument('--query', type=str, help='Query text (optional)')
    parser.add_argument('--namespace', type=str, default='tx', help='Pinecone namespace (default: tx)')
    parser.add_argument('--top_k', type=int, default=5, help='Number of results to return (default: 5)')
    args = parser.parse_args()
    
    # Use command line args or prompt for query
    namespace = args.namespace
    user_query = args.query if args.query else input("Enter your question about Texas personal injury law: ")

    # Step 1: Retrieve relevant information from Pinecone
    logging.info("Searching Pinecone for relevant legal texts...")
    matches = search_pinecone(user_query, namespace)

    if not matches:
        print("[Info] No relevant legal texts found for this query.")
    else:
        # Format context with relevance scores and citations
        context = format_context(matches)
        logging.debug("Constructed context with citations:\n%s", context)

        # Step 2: Get answer from LLM with proper citations
        logging.info("Generating answer with proper legal citations...")
        answer = ask_llm(user_query, context)
        
        print("\n=== Texas Personal Injury Law Analysis ===\n")
        print(f"{answer}\n")
        print("Note: All citations reference Texas personal injury laws and statutes.")
