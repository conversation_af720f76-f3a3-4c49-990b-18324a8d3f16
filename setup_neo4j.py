"""
Neo4j Setup Script - Initializes database schema for legal document graph
"""

import os
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Neo4j credentials
NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

def initialize_neo4j_schema():
    """Create necessary constraints and indexes in Neo4j database"""
    with GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD)) as driver:
        with driver.session() as session:
            # Document constraints and indexes
            print("Creating document constraints and indexes...")
            
            # Unique constraints
            session.run("CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.document_id IS UNIQUE")
            session.run("CREATE CONSTRAINT citation_text IF NOT EXISTS FOR (c:Citation) REQUIRE c.text IS UNIQUE")
            
            # Regular indexes for performant queries
            session.run("CREATE INDEX document_jurisdiction IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction)")
            session.run("CREATE INDEX document_type IF NOT EXISTS FOR (d:Document) ON (d.doc_type)")
            session.run("CREATE INDEX document_title IF NOT EXISTS FOR (d:Document) ON (d.title)")
            session.run("CREATE INDEX citation_type IF NOT EXISTS FOR (c:Citation) ON (c.type)")
            
            # Document type specific indexes
            session.run("CREATE INDEX case_citation IF NOT EXISTS FOR (d:Document) ON (d.case_citation)")
            session.run("CREATE INDEX statute_section IF NOT EXISTS FOR (d:Document) ON (d.statute_section)")
            
            # Create initial jurisdiction nodes
            print("Creating jurisdiction nodes...")
            jurisdictions = [
                "Texas", "California", "New York", "Florida", 
                "Ohio", "Illinois", "Pennsylvania", "Michigan", "Federal"
            ]
            
            for jurisdiction in jurisdictions:
                session.run("""
                MERGE (j:Jurisdiction {name: $name})
                SET j.code = $code,
                    j.created_at = datetime()
                """, name=jurisdiction, code=jurisdiction.lower()[:2])
            
            # Create document type nodes
            print("Creating document type nodes...")
            doc_types = [
                {"name": "law", "description": "Statutory law"},
                {"name": "precedent_case", "description": "Court cases establishing legal precedent"},
                {"name": "regulation", "description": "Administrative regulations"},
                {"name": "administrative_ruling", "description": "Agency decisions"}
            ]
            
            for doc_type in doc_types:
                session.run("""
                MERGE (dt:DocumentType {name: $name})
                SET dt.description = $description,
                    dt.created_at = datetime()
                """, name=doc_type["name"], description=doc_type["description"])
            
            print("Neo4j schema initialization complete!")
            
            # Test query to count nodes
            result = session.run("MATCH (n) RETURN labels(n) AS label, count(*) AS count")
            print("\nDatabase Node Counts:")
            for record in result:
                print(f"  {record['label'][0]}: {record['count']}")

if __name__ == "__main__":
    print(f"Connecting to Neo4j at {NEO4J_URI}...")
    try:
        initialize_neo4j_schema()
        print("Neo4j setup completed successfully.")
    except Exception as e:
        print(f"Error setting up Neo4j: {str(e)}")
