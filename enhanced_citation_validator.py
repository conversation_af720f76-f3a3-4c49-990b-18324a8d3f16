#!/usr/bin/env python3
"""
Enhanced Citation Validator Module

This module builds on the citation_validator.py and citation_classifier.py
to provide advanced validation capabilities for legal citations.
It handles different citation types appropriately and provides
contextual validation.
"""

import os
import re
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

from citation_classifier import CitationClassifier, CitationType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCitationValidator:
    """
    Enhanced citation validator that uses citation classification
    to provide more nuanced validation for different types of legal citations.
    """
    
    def __init__(self):
        """Initialize the citation validator with Neo4j connection"""
        load_dotenv()
        
        self.uri = os.getenv("NEO4J_URI")
        self.user = os.getenv("NEO4J_USER")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not all([self.uri, self.user, self.password]):
            raise ValueError("Neo4j connection details not found in environment variables")
        
        self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
        self.classifier = CitationClassifier()
        
        # Check database connection
        self._check_connection()
    
    def _check_connection(self):
        """Verify Neo4j connection is working"""
        try:
            with self.driver.session() as session:
                result = session.run("MATCH (n) RETURN count(n) AS count LIMIT 1")
                count = result.single()["count"]
                logger.info(f"Connected to Neo4j database with {count} nodes")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            raise ConnectionError(f"Cannot connect to Neo4j database: {str(e)}")
    
    def close(self):
        """Close the Neo4j driver connection"""
        if self.driver:
            self.driver.close()
    
    def validate_citation(self, citation_text: str, document_id: Optional[str] = None, 
                          context: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate a single citation with enhanced type-specific validation
        
        Args:
            citation_text: The text of the citation to validate
            document_id: Optional ID of the document containing the citation
            context: Optional surrounding text for context-aware validation
            
        Returns:
            A dictionary with validation results and suggestions
        """
        # First classify the citation
        classification = self.classifier.get_classification_details(citation_text, context)
        citation_type = CitationType(classification["classification"]["type"])
        
        # Base validation result
        validation_result = {
            "citation_text": citation_text,
            "document_id": document_id,
            "classification": classification["classification"],
            "components": classification["components"],
            "is_valid": False,
            "validation_method": "unknown",
            "validation_details": {},
            "suggestions": []
        }
        
        # Validate based on citation type
        if citation_type == CitationType.INTERNAL_SECTION:
            self._validate_internal_section(validation_result)
            
        elif citation_type == CitationType.LEGISLATIVE_HISTORY:
            # Legislative history citations don't need to resolve to documents
            validation_result["is_valid"] = True
            validation_result["validation_method"] = "legislative_history"
            validation_result["validation_details"]["notes"] = "Legislative history citations are considered valid without document resolution"
            
        elif citation_type == CitationType.CASE_CITATION:
            self._validate_case_citation(validation_result)
            
        elif citation_type == CitationType.EXTERNAL_STATUTE:
            self._validate_external_statute(validation_result)
            
        elif citation_type == CitationType.CONSTITUTION:
            self._validate_constitution_citation(validation_result)
            
        elif citation_type == CitationType.LEGAL_DOCTRINE:
            # Legal doctrines don't need to resolve to documents
            validation_result["is_valid"] = True
            validation_result["validation_method"] = "legal_doctrine"
            validation_result["validation_details"]["notes"] = "Legal doctrine citations are considered valid without document resolution"
            
        else:
            # For other types, check if they resolve to any document
            document_connections = self._check_document_connections(citation_text)
            if document_connections:
                validation_result["is_valid"] = True
                validation_result["validation_method"] = "document_resolution"
                validation_result["validation_details"]["resolved_documents"] = document_connections
            else:
                validation_result["validation_method"] = "unresolved"
                validation_result["suggestions"] = self._generate_suggestions(citation_text, citation_type)
        
        return validation_result
    
    def _validate_internal_section(self, validation_result: Dict[str, Any]):
        """Validate internal section references"""
        citation_text = validation_result["citation_text"]
        components = validation_result["components"]
        
        # Try to find matching section in database
        section_number = components.get("section_number")
        if section_number:
            # Find documents with matching section numbers
            with self.driver.session() as session:
                query = """
                MATCH (d:Document)
                WHERE d.title CONTAINS $section_number OR d.content CONTAINS $section_number
                RETURN d.document_id AS document_id, d.title AS title
                LIMIT 5
                """
                result = session.run(query, section_number=section_number)
                matches = [record for record in result]
                
                if matches:
                    validation_result["is_valid"] = True
                    validation_result["validation_method"] = "section_number_match"
                    validation_result["validation_details"]["matching_documents"] = matches
                else:
                    # Check if any Citations with similar text resolve to documents
                    similar_citations = self._find_similar_citations(citation_text)
                    if similar_citations:
                        validation_result["validation_method"] = "similar_citations"
                        validation_result["validation_details"]["similar_citations"] = similar_citations
                        validation_result["suggestions"].append(
                            f"Found similar citations that resolve to documents")
                    else:
                        validation_result["validation_method"] = "unresolved"
                        validation_result["suggestions"].append(
                            f"No documents found containing section {section_number}")
        else:
            validation_result["validation_method"] = "incomplete"
            validation_result["suggestions"].append("Citation appears to be incomplete or malformed")
    
    def _validate_case_citation(self, validation_result: Dict[str, Any]):
        """Validate case citations"""
        citation_text = validation_result["citation_text"]
        components = validation_result["components"]
        
        # Check for case name in database
        plaintiff = components.get("plaintiff")
        defendant = components.get("defendant")
        
        if plaintiff and defendant:
            case_name = f"{plaintiff} v. {defendant}"
            
            with self.driver.session() as session:
                # Look for documents that might be this case
                query = """
                MATCH (d:Document) 
                WHERE d.title CONTAINS $plaintiff AND d.title CONTAINS $defendant
                OR d.content CONTAINS $case_name
                RETURN d.document_id AS document_id, d.title AS title, d.doc_type AS doc_type
                LIMIT 5
                """
                result = session.run(query, plaintiff=plaintiff, defendant=defendant, case_name=case_name)
                matches = [record for record in result]
                
                if matches:
                    validation_result["is_valid"] = True
                    validation_result["validation_method"] = "case_name_match"
                    validation_result["validation_details"]["matching_documents"] = matches
                else:
                    # Check if any citations with this text already resolve to documents
                    document_connections = self._check_document_connections(citation_text)
                    if document_connections:
                        validation_result["is_valid"] = True
                        validation_result["validation_method"] = "existing_resolution"
                        validation_result["validation_details"]["resolved_documents"] = document_connections
                    else:
                        validation_result["validation_method"] = "unresolved"
                        validation_result["suggestions"].append(
                            f"Case '{case_name}' not found in document database")
        else:
            # May be a non-standard case citation format
            document_connections = self._check_document_connections(citation_text)
            if document_connections:
                validation_result["is_valid"] = True
                validation_result["validation_method"] = "existing_resolution"
                validation_result["validation_details"]["resolved_documents"] = document_connections
            else:
                validation_result["validation_method"] = "incomplete"
                validation_result["suggestions"].append("Case citation appears to be incomplete or in non-standard format")
    
    def _validate_external_statute(self, validation_result: Dict[str, Any]):
        """Validate external statute references"""
        citation_text = validation_result["citation_text"]
        components = validation_result["components"]
        
        code_name = components.get("code_name")
        section = components.get("section")
        
        if code_name and section:
            with self.driver.session() as session:
                # Look for documents that might be this statute
                query = """
                MATCH (d:Document) 
                WHERE d.title CONTAINS $code_name
                AND (d.title CONTAINS $section OR d.content CONTAINS $section)
                RETURN d.document_id AS document_id, d.title AS title
                LIMIT 5
                """
                result = session.run(query, code_name=code_name, section=section)
                matches = [record for record in result]
                
                if matches:
                    validation_result["is_valid"] = True
                    validation_result["validation_method"] = "statute_match"
                    validation_result["validation_details"]["matching_documents"] = matches
                else:
                    # External statutes that don't resolve to documents can still be valid
                    validation_result["is_valid"] = True
                    validation_result["validation_method"] = "external_reference"
                    validation_result["validation_details"]["notes"] = f"External statute {code_name} § {section} is accepted as valid"
                    validation_result["suggestions"].append(
                        f"Consider adding {code_name} Code documents to improve citation resolution")
        else:
            document_connections = self._check_document_connections(citation_text)
            if document_connections:
                validation_result["is_valid"] = True
                validation_result["validation_method"] = "existing_resolution"
                validation_result["validation_details"]["resolved_documents"] = document_connections
            else:
                validation_result["validation_method"] = "incomplete"
                validation_result["suggestions"].append("Statute citation appears to be incomplete")
    
    def _validate_constitution_citation(self, validation_result: Dict[str, Any]):
        """Validate constitutional citations"""
        citation_text = validation_result["citation_text"]
        components = validation_result["components"]
        
        # Constitutional citations are usually valid as external references
        validation_result["is_valid"] = True
        validation_result["validation_method"] = "constitution_reference"
        
        # Check if we have the constitutional text in our database
        article = components.get("article")
        section = components.get("section")
        
        if article or section:
            with self.driver.session() as session:
                # Look for constitutional documents
                query = """
                MATCH (d:Document) 
                WHERE d.title CONTAINS "Constitution" 
                RETURN d.document_id AS document_id, d.title AS title
                LIMIT 5
                """
                result = session.run(query)
                matches = [record for record in result]
                
                if matches:
                    validation_result["validation_details"]["constitutional_documents"] = matches
                    validation_result["suggestions"].append(
                        f"Constitutional provisions found in database, consider linking this citation")
                else:
                    validation_result["validation_details"]["notes"] = "Constitutional reference is valid, but no constitutional documents found in database"
                    validation_result["suggestions"].append(
                        "Consider adding constitutional documents to improve citation resolution")
        else:
            document_connections = self._check_document_connections(citation_text)
            if document_connections:
                validation_result["validation_method"] = "existing_resolution"
                validation_result["validation_details"]["resolved_documents"] = document_connections
    
    def _check_document_connections(self, citation_text: str) -> List[Dict[str, Any]]:
        """Check if a citation text already resolves to any documents"""
        with self.driver.session() as session:
            query = """
            MATCH (c:Citation {text: $citation_text})-[:RESOLVES_TO]->(d:Document)
            RETURN d.document_id AS document_id, d.title AS title, 
                   d.doc_type AS doc_type
            LIMIT 5
            """
            result = session.run(query, citation_text=citation_text)
            return [record for record in result]
    
    def _find_similar_citations(self, citation_text: str) -> List[Dict[str, Any]]:
        """Find citations similar to the given text that do resolve to documents"""
        with self.driver.session() as session:
            # Use fuzzy matching to find similar citations
            query = """
            MATCH (c:Citation)-[:RESOLVES_TO]->(d:Document)
            WHERE apoc.text.fuzzyMatch(c.text, $citation_text) > 0.7
            RETURN c.text AS citation_text, d.document_id AS document_id,
                   d.title AS document_title, 
                   apoc.text.fuzzyMatch(c.text, $citation_text) AS similarity
            ORDER BY similarity DESC
            LIMIT 5
            """
            try:
                result = session.run(query, citation_text=citation_text)
                return [record for record in result]
            except Exception as e:
                logger.warning(f"Error in fuzzy matching: {str(e)}")
                # Fall back to simple contains matching if fuzzy match fails
                query = """
                MATCH (c:Citation)-[:RESOLVES_TO]->(d:Document)
                WHERE c.text CONTAINS $search_term
                RETURN c.text AS citation_text, d.document_id AS document_id,
                       d.title AS document_title
                LIMIT 5
                """
                search_term = citation_text.split()[0] if citation_text.split() else ""
                if search_term:
                    result = session.run(query, search_term=search_term)
                    return [record for record in result]
                return []
    
    def _generate_suggestions(self, citation_text: str, citation_type: CitationType) -> List[str]:
        """Generate suggestions for unresolved citations based on their type"""
        suggestions = []
        
        if citation_type == CitationType.INTERNAL_SECTION:
            suggestions.append("This appears to be a section reference that could not be resolved")
            suggestions.append("Check if the section number is correct or if the referenced document exists in the database")
            
        elif citation_type == CitationType.CASE_CITATION:
            suggestions.append("This appears to be a case citation that could not be resolved")
            suggestions.append("Consider adding the case document to your database")
            
        elif citation_type == CitationType.EXTERNAL_STATUTE:
            suggestions.append("This appears to be a reference to an external statute")
            suggestions.append("Consider adding the statute document to your database for better resolution")
            
        elif citation_type == CitationType.PROCEDURAL_RULE:
            suggestions.append("This appears to be a procedural rule citation")
            suggestions.append("Consider adding rule documents to your database")
            
        else:
            suggestions.append("Citation could not be resolved to any document")
            suggestions.append("Consider reviewing citation format or adding relevant documents")
        
        return suggestions
    
    def validate_document_citations(self, document_id: str) -> Dict[str, Any]:
        """
        Validate all citations in a specific document
        
        Args:
            document_id: ID of the document to analyze
            
        Returns:
            Dictionary with validation results for all citations in the document
        """
        with self.driver.session() as session:
            # Get the document and its citations
            query = """
            MATCH (d:Document {document_id: $document_id})-[:CITES]->(c:Citation)
            OPTIONAL MATCH (c)-[:RESOLVES_TO]->(d2:Document)
            RETURN c.id AS citation_id, c.text AS citation_text, 
                   c.confidence AS confidence,
                   d2.document_id AS resolved_document_id,
                   d2.title AS resolved_document_title
            """
            result = session.run(query, document_id=document_id)
            citations = [record for record in result]
            
            # Get document content for context
            doc_query = """
            MATCH (d:Document {document_id: $document_id})
            RETURN d.content AS content, d.title AS title
            """
            doc_result = session.run(doc_query, document_id=document_id)
            doc_record = doc_result.single()
            
            if not doc_record:
                return {
                    "document_id": document_id,
                    "error": f"Document with ID {document_id} not found"
                }
            
            document_content = doc_record.get("content", "")
            document_title = doc_record.get("title", "Unknown")
        
        # Validate each citation
        validated_citations = []
        citation_types = {}
        resolution_count = 0
        
        for citation in citations:
            citation_text = citation.get("citation_text", "")
            if not citation_text:
                continue
                
            # Get context for this citation if possible
            context = self._extract_context(document_content, citation_text)
            
            # Validate the citation
            validation_result = self.validate_citation(
                citation_text=citation_text,
                document_id=document_id,
                context=context
            )
            
            # Add citation id from database
            validation_result["citation_id"] = citation.get("citation_id")
            validation_result["resolved_document_id"] = citation.get("resolved_document_id")
            
            # Keep track of citation types
            citation_type = validation_result["classification"]["type"]
            citation_types[citation_type] = citation_types.get(citation_type, 0) + 1
            
            if validation_result["is_valid"]:
                resolution_count += 1
                
            validated_citations.append(validation_result)
        
        # Calculate statistics
        total_citations = len(validated_citations)
        resolution_rate = resolution_count / total_citations if total_citations > 0 else 0
        
        # Generate summary
        return {
            "document_id": document_id,
            "document_title": document_title,
            "total_citations": total_citations,
            "resolved_citations": resolution_count,
            "resolution_rate": resolution_rate,
            "citation_types": citation_types,
            "validated_citations": validated_citations,
            "timestamp": datetime.now().isoformat()
        }
    
    def _extract_context(self, document_content: str, citation_text: str, window: int = 200) -> str:
        """
        Extract text around a citation to provide context
        
        Args:
            document_content: The full text of the document
            citation_text: The citation text to find
            window: Characters to include before and after the citation
            
        Returns:
            The extracted context or empty string if not found
        """
        if not document_content or not citation_text:
            return ""
            
        try:
            citation_index = document_content.find(citation_text)
            if citation_index >= 0:
                start = max(0, citation_index - window)
                end = min(len(document_content), citation_index + len(citation_text) + window)
                return document_content[start:end]
        except Exception as e:
            logger.warning(f"Error extracting context: {str(e)}")
            
        return ""
    
    def batch_validate_citations(self, limit: int = 100) -> Dict[str, Any]:
        """
        Validate a batch of citations across the database
        
        Args:
            limit: Maximum number of citations to process
            
        Returns:
            Dictionary with validation results
        """
        with self.driver.session() as session:
            # Get a batch of citations
            query = f"""
            MATCH (d:Document)-[:CITES]->(c:Citation)
            WHERE c.text IS NOT NULL
            RETURN c.id AS citation_id, c.text AS citation_text, 
                   d.document_id AS document_id, d.title AS document_title,
                   d.content AS document_content
            LIMIT {limit}
            """
            result = session.run(query)
            citations = [record for record in result]
        
        # Validate each citation
        validation_results = []
        citation_types = {}
        valid_count = 0
        
        for citation in citations:
            citation_text = citation.get("citation_text", "")
            document_id = citation.get("document_id", "")
            document_content = citation.get("document_content", "")
            
            # Extract context if possible
            context = self._extract_context(document_content, citation_text)
            
            # Validate the citation
            validation_result = self.validate_citation(
                citation_text=citation_text,
                document_id=document_id,
                context=context
            )
            
            # Add citation id and document title
            validation_result["citation_id"] = citation.get("citation_id")
            validation_result["document_title"] = citation.get("document_title")
            
            # Keep track of citation types
            citation_type = validation_result["classification"]["type"]
            citation_types[citation_type] = citation_types.get(citation_type, 0) + 1
            
            if validation_result["is_valid"]:
                valid_count += 1
                
            validation_results.append(validation_result)
        
        # Calculate statistics
        total_citations = len(validation_results)
        valid_rate = valid_count / total_citations if total_citations > 0 else 0
        
        return {
            "total_citations": total_citations,
            "valid_citations": valid_count,
            "valid_rate": valid_rate,
            "citation_types": citation_types,
            "validation_results": validation_results,
            "timestamp": datetime.now().isoformat()
        }

    def generate_validation_report(self, document_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive citation validation report
        
        Args:
            document_id: Optional document ID to focus the report on
            
        Returns:
            Dictionary with comprehensive validation statistics and insights
        """
        # If document_id provided, validate that specific document
        if document_id:
            return self.validate_document_citations(document_id)
        
        # Otherwise generate a database-wide report
        with self.driver.session() as session:
            # Get general citation statistics
            stats_query = """
            MATCH (c:Citation)
            OPTIONAL MATCH (c)-[:RESOLVES_TO]->(d:Document)
            WITH count(c) AS total_citations,
                 count(d) AS resolved_citations
            RETURN total_citations, resolved_citations,
                   toFloat(resolved_citations) / total_citations AS resolution_rate
            """
            stats_result = session.run(stats_query)
            stats = stats_result.single()
            
            if not stats:
                return {
                    "error": "Failed to retrieve citation statistics",
                    "timestamp": datetime.now().isoformat()
                }
            
            # Get citation type distribution with sample batch
            sample_result = self.batch_validate_citations(limit=100)
            
            # Find documents with low citation resolution rates
            low_resolution_query = """
            MATCH (d:Document)-[:CITES]->(c:Citation)
            WITH d, count(c) AS total_cites
            OPTIONAL MATCH (d)-[:CITES]->(c2:Citation)-[:RESOLVES_TO]->(:Document)
            WITH d, total_cites, count(c2) AS resolved_cites
            WHERE total_cites > 5 AND toFloat(resolved_cites) / total_cites < 0.5
            RETURN d.document_id AS document_id, d.title AS title,
                   total_cites, resolved_cites,
                   toFloat(resolved_cites) / total_cites AS resolution_rate
            ORDER BY resolution_rate ASC
            LIMIT 10
            """
            low_resolution_result = session.run(low_resolution_query)
            low_resolution_docs = [record for record in low_resolution_result]
            
            # Find most cited documents
            top_cited_query = """
            MATCH (d:Document)<-[:RESOLVES_TO]-(c:Citation)
            WITH d, count(c) AS citation_count
            ORDER BY citation_count DESC
            LIMIT 10
            RETURN d.document_id AS document_id, d.title AS title,
                   citation_count
            """
            top_cited_result = session.run(top_cited_query)
            top_cited_docs = [record for record in top_cited_result]
        
        # Generate insights and recommendations
        insights = []
        
        # Overall resolution rate insight
        resolution_rate = stats["resolution_rate"]
        if resolution_rate < 0.3:
            insights.append("Low overall citation resolution rate. Consider improving citation extraction and validation.")
        elif resolution_rate < 0.6:
            insights.append("Moderate citation resolution rate. Focus on resolving citations in key documents.")
        else:
            insights.append("Good citation resolution rate. Continue maintaining citation quality.")
        
        # Document-specific insights
        if low_resolution_docs:
            insights.append(f"Found {len(low_resolution_docs)} documents with low citation resolution rates.")
            insights.append("Consider reviewing these documents to improve citation quality.")
        
        # Citation type insights
        citation_types = sample_result["citation_types"]
        for citation_type, count in citation_types.items():
            if citation_type == "legislative_history" and count > 10:
                insights.append("High number of legislative history citations. These are expected to remain unresolved.")
            elif citation_type == "case_citation" and count > 10:
                insights.append("Consider adding more case documents to improve case citation resolution.")
            elif citation_type == "external_statute" and count > 10:
                insights.append("Consider adding more statute documents to improve statutory citation resolution.")
        
        return {
            "validation_summary": {
                "total_citations": stats["total_citations"],
                "resolved_citations": stats["resolved_citations"],
                "resolution_rate": stats["resolution_rate"]
            },
            "citation_types": sample_result["citation_types"],
            "low_resolution_documents": low_resolution_docs,
            "top_cited_documents": top_cited_docs,
            "insights": insights,
            "timestamp": datetime.now().isoformat()
        }

# Example usage if run directly
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced Citation Validator")
    parser.add_argument("--citation", help="Citation text to validate")
    parser.add_argument("--document", help="Document ID to validate citations for")
    parser.add_argument("--report", action="store_true", help="Generate validation report")
    parser.add_argument("--batch", action="store_true", help="Run batch validation")
    parser.add_argument("--limit", type=int, default=100, help="Limit for batch operations")
    
    args = parser.parse_args()
    
    validator = EnhancedCitationValidator()
    
    try:
        if args.citation:
            result = validator.validate_citation(args.citation)
            print(f"Citation: {args.citation}")
            print(f"Type: {result['classification']['type']}")
            print(f"Confidence: {result['classification']['confidence']:.2f}")
            print(f"Valid: {result['is_valid']}")
            print(f"Validation method: {result['validation_method']}")
            print(f"Suggestions: {result['suggestions']}")
            
        elif args.document:
            result = validator.validate_document_citations(args.document)
            print(f"Document: {result['document_title']}")
            print(f"Total citations: {result['total_citations']}")
            print(f"Resolved citations: {result['resolved_citations']}")
            print(f"Resolution rate: {result['resolution_rate']:.2f}")
            print(f"Citation types: {result['citation_types']}")
            
        elif args.batch:
            result = validator.batch_validate_citations(args.limit)
            print(f"Batch validation complete")
            print(f"Total citations: {result['total_citations']}")
            print(f"Valid citations: {result['valid_citations']}")
            print(f"Valid rate: {result['valid_rate']:.2f}")
            print(f"Citation types: {result['citation_types']}")
            
        elif args.report:
            result = validator.generate_validation_report(args.document)
            if args.document:
                print(f"Document validation report: {result['document_title']}")
                print(f"Total citations: {result['total_citations']}")
                print(f"Resolved citations: {result['resolved_citations']}")
                print(f"Resolution rate: {result['resolution_rate']:.2f}")
            else:
                print("Database-wide validation report")
                print(f"Total citations: {result['validation_summary']['total_citations']}")
                print(f"Resolved citations: {result['validation_summary']['resolved_citations']}")
                print(f"Resolution rate: {result['validation_summary']['resolution_rate']:.2f}")
                
            print("\nInsights:")
            for insight in result.get("insights", []):
                print(f"- {insight}")
                
        else:
            print("No action specified. Use --citation, --document, --batch, or --report.")
            
    finally:
        validator.close()
