"""
Document Graph System for Legal Document Relationships
Uses Neo4j to create a graph database of document relationships
"""

import os
import re
import uuid
import json
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Use Neo4j credentials from env or default to localhost
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

class LegalDocumentGraph:
    """Build and maintain a Neo4j graph of legal document relationships"""
    
    def __init__(self, uri=NEO4J_URI, user=NEO4J_USER, password=NEO4J_PASSWORD):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self._initialize_schema()
    
    def _initialize_schema(self):
        """Initialize Neo4j schema with constraints and indexes"""
        with self.driver.session() as session:
            # Create constraints
            session.run("CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.document_id IS UNIQUE")
            session.run("CREATE CONSTRAINT citation_text IF NOT EXISTS FOR (c:Citation) REQUIRE c.text IS UNIQUE")
            
            # Create indexes
            session.run("CREATE INDEX document_jurisdiction IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction)")
            session.run("CREATE INDEX document_type IF NOT EXISTS FOR (d:Document) ON (d.doc_type)")
    
    def create_document_node(self, document_id, metadata):
        """Create a node representing a legal document"""
        with self.driver.session() as session:
            # Build dynamic properties from metadata
            props = {
                "document_id": document_id,
                "title": metadata.get("document_title", ""),
                "doc_type": metadata.get("doc_type", ""),
                "jurisdiction": metadata.get("jurisdiction", ""),
                "created_at": datetime.now().isoformat()
            }
            
            # Add type-specific properties
            if metadata.get("doc_type") == "precedent_case":
                props.update({
                    "case_number": metadata.get("case_number", ""),
                    "court": metadata.get("court", ""),
                    "case_date": metadata.get("case_date", ""),
                    "case_citation": metadata.get("case_citation", "")
                })
            elif metadata.get("doc_type") == "law":
                props.update({
                    "statute_title": metadata.get("statute_title", ""),
                    "statute_chapter": metadata.get("statute_chapter", ""),
                    "statute_section": metadata.get("statute_section", "")
                })
            
            # Create or update document node
            result = session.run("""
            MERGE (d:Document {document_id: $document_id})
            SET d += $props
            RETURN d
            """, document_id=document_id, props=props)
            
            return result.single()
    
    def extract_citations(self, content, doc_type):
        """Extract citations from document content based on doc type"""
        citations = []
        
        if doc_type == "precedent_case":
            # Look for case citations
            case_patterns = [
                r'([A-Z][a-z]+\s+v\.\s+[A-Z][a-z]+)',  # Basic case name (Smith v. Jones)
                r'(\d+\s+[A-Z][a-z]+\.(?:\s+\d+)?)',   # Reporter citations (123 S.Ct. 456)
                r'(\d+\s+[A-Z]\.(?:\w+\.)+\s+\d+)'     # Complex citations (123 F.3d 456)
            ]
            
            for pattern in case_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    citation_text = match.group(1)
                    context = content[max(0, match.start() - 100):min(len(content), match.end() + 100)]
                    citations.append({
                        "text": citation_text,
                        "type": "case_citation",
                        "context": context
                    })
        
        elif doc_type == "law":
            # Look for statute citations
            statute_patterns = [
                r'(?:Section|§)\s+(\d+\.\d+)',          # Section numbers
                r'(?:Chapter|Ch\.)\s+(\d+[A-Z]?)',      # Chapter references
                r'(\d+\s+[A-Z][a-z]+\.?\s+Code\s+§\s+\d+)'  # Code references
            ]
            
            for pattern in statute_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    citation_text = match.group(0)
                    context = content[max(0, match.start() - 100):min(len(content), match.end() + 100)]
                    citations.append({
                        "text": citation_text,
                        "type": "statute_citation",
                        "context": context
                    })
        
        return citations
    
    def link_document_citations(self, document_id, content, doc_type):
        """Extract citations from document content and create relationships"""
        citations = self.extract_citations(content, doc_type)
        
        with self.driver.session() as session:
            # Create citation nodes and relationships
            for citation in citations:
                result = session.run("""
                MATCH (source:Document {document_id: $source_id})
                MERGE (target:Citation {text: $citation_text})
                ON CREATE SET target.type = $citation_type, 
                               target.created_at = datetime()
                MERGE (source)-[r:CITES]->(target)
                SET r.context = $context
                RETURN r
                """, source_id=document_id, citation_text=citation["text"],
                citation_type=citation["type"], context=citation["context"])
        
        return citations
    
    def resolve_citations(self):
        """Link citation nodes to actual document nodes when found"""
        with self.driver.session() as session:
            result = session.run("""
            MATCH (c:Citation)
            MATCH (d:Document)
            WHERE 
                CASE c.type
                    WHEN 'case_citation' THEN 
                        d.doc_type = 'precedent_case' AND
                        (c.text CONTAINS d.title OR d.case_citation CONTAINS c.text)
                    WHEN 'statute_citation' THEN
                        d.doc_type = 'law' AND
                        (c.text CONTAINS d.statute_section OR 
                         c.text CONTAINS d.statute_chapter)
                    ELSE false
                END
            MERGE (c)-[r:RESOLVES_TO]->(d)
            RETURN count(r) as resolved_count
            """)
            
            return result.single()["resolved_count"]
    
    def build_related_documents(self, document_id, max_depth=2):
        """Find related documents based on citations and shared topics"""
        with self.driver.session() as session:
            # First, find directly cited documents
            result = session.run("""
            MATCH (d:Document {document_id: $document_id})-[:CITES]->
                  (c:Citation)-[:RESOLVES_TO]->(cited:Document)
            RETURN cited.document_id as related_id, 
                   cited.title as related_title,
                   'direct_citation' as relationship_type,
                   cited.doc_type as doc_type
            """, document_id=document_id)
            
            direct_citations = [dict(record) for record in result]
            
            # Next, find documents that cite the same sources (co-citation)
            result = session.run("""
            MATCH (d:Document {document_id: $document_id})-[:CITES]->(c:Citation)<-[:CITES]-(other:Document)
            WHERE other.document_id <> $document_id
            RETURN other.document_id as related_id, 
                   other.title as related_title,
                   'co_citation' as relationship_type,
                   other.doc_type as doc_type,
                   count(c) as strength
            ORDER BY strength DESC
            """, document_id=document_id)
            
            co_citations = [dict(record) for record in result]
            
            # Finally, find documents that are cited by the same documents (bibliographic coupling)
            result = session.run("""
            MATCH (citing:Document)-[:CITES]->(:Citation)-[:RESOLVES_TO]->(d:Document {document_id: $document_id})
            MATCH (citing)-[:CITES]->(:Citation)-[:RESOLVES_TO]->(other:Document)
            WHERE other.document_id <> $document_id
            RETURN other.document_id as related_id, 
                   other.title as related_title,
                   'bibliographic_coupling' as relationship_type,
                   other.doc_type as doc_type,
                   count(citing) as strength
            ORDER BY strength DESC
            """, document_id=document_id)
            
            bibliographic_coupling = [dict(record) for record in result]
            
            return {
                "direct_citations": direct_citations,
                "co_citations": co_citations,
                "bibliographic_coupling": bibliographic_coupling
            }
    
    def get_document_network(self, document_id, depth=2):
        """Retrieve the network of related documents as a graph"""
        with self.driver.session() as session:
            result = session.run("""
            MATCH path = (d:Document {document_id: $document_id})-[*1..%d]-(related)
            RETURN path
            LIMIT 100
            """ % depth, document_id=document_id)
            
            # Format results for visualization
            nodes = {}
            relationships = []
            
            for record in result:
                path = record["path"]
                for segment in path:
                    if hasattr(segment, "start") and hasattr(segment, "end"):
                        # This is a relationship
                        start_id = segment.start_node.id
                        end_id = segment.end_node.id
                        rel_type = segment.type
                        
                        relationships.append({
                            "source": start_id,
                            "target": end_id,
                            "type": rel_type
                        })
                    else:
                        # This is a node
                        node_id = segment.id
                        labels = list(segment.labels)
                        properties = dict(segment)
                        
                        nodes[node_id] = {
                            "id": node_id,
                            "labels": labels,
                            "properties": properties
                        }
            
            return {
                "nodes": list(nodes.values()),
                "relationships": relationships
            }
    
    def get_citation_stats(self, jurisdiction=None, doc_type=None):
        """Get statistics about citations in the document graph"""
        query = """
        MATCH (d:Document)-[r:CITES]->(c:Citation)
        """
        
        if jurisdiction or doc_type:
            query += "WHERE "
            conditions = []
            if jurisdiction:
                conditions.append("d.jurisdiction = $jurisdiction")
            if doc_type:
                conditions.append("d.doc_type = $doc_type")
            query += " AND ".join(conditions)
        
        query += """
        RETURN 
            d.jurisdiction as jurisdiction,
            d.doc_type as doc_type,
            count(r) as citation_count,
            count(DISTINCT d) as document_count,
            count(DISTINCT c) as unique_citations,
            1.0 * count(r) / count(DISTINCT d) as avg_citations_per_doc
        GROUP BY d.jurisdiction, d.doc_type
        ORDER BY citation_count DESC
        """
        
        with self.driver.session() as session:
            result = session.run(query, jurisdiction=jurisdiction, doc_type=doc_type)
            return [dict(record) for record in result]
    
    def close(self):
        """Close the Neo4j driver"""
        self.driver.close()


# Helper function to set up Neo4j with the document
def setup_document_in_graph(document_id, pdf_path, content, metadata):
    """Set up a document in the Neo4j graph database"""
    graph = LegalDocumentGraph()
    
    try:
        # Create document node
        graph.create_document_node(document_id, metadata)
        
        # Extract and link citations
        doc_type = metadata.get("doc_type", "law")
        citations = graph.link_document_citations(document_id, content, doc_type)
        
        # Resolve citations to actual documents
        resolved_count = graph.resolve_citations()
        
        return {
            "document_id": document_id,
            "citations_found": len(citations),
            "citations_resolved": resolved_count
        }
    finally:
        graph.close()


# Example usage
if __name__ == "__main__":
    # Test function
    pass
